plugins {
	id 'java'
	id 'org.springframework.boot' version '3.3.10'
	id 'io.spring.dependency-management' version '1.1.7'
}

group = 'kr.wayplus'
version = '0.0.1-SNAPSHOT'

java {
	toolchain {
		languageVersion = JavaLanguageVersion.of(17)
	}
}

configurations {
	compileOnly {
		extendsFrom annotationProcessor
	}
}

repositories {
	mavenCentral()
}

dependencies {
	implementation 'org.springframework.boot:spring-boot-starter-security'
	implementation 'org.springframework.boot:spring-boot-starter-validation'
	implementation 'org.springframework.boot:spring-boot-starter-web'
	implementation 'org.mybatis.spring.boot:mybatis-spring-boot-starter:3.0.4'
    implementation 'org.springframework.data:spring-data-commons' // Spring Data 페이징 타입 사용을 위해 추가
	compileOnly 'org.projectlombok:lombok'
	developmentOnly 'org.springframework.boot:spring-boot-devtools'
	runtimeOnly 'com.mysql:mysql-connector-j'
	annotationProcessor 'org.projectlombok:lombok'
	testImplementation 'org.springframework.boot:spring-boot-starter-test'
	testImplementation 'org.mybatis.spring.boot:mybatis-spring-boot-starter-test:3.0.4'
	testImplementation 'org.springframework.security:spring-security-test'
	testRuntimeOnly 'org.junit.platform:junit-platform-launcher'
	// JWT
	implementation 'io.jsonwebtoken:jjwt-api:0.12.5'
	runtimeOnly 'io.jsonwebtoken:jjwt-impl:0.12.5'
	runtimeOnly 'io.jsonwebtoken:jjwt-jackson:0.12.5'
    // Swagger UI (Springdoc OpenAPI)
    implementation 'org.springdoc:springdoc-openapi-starter-webmvc-ui:2.5.0'
	// QR관련 라이브러리 추가
    implementation 'net.glxn.qrgen:javase:2.0'
    implementation 'org.apache.pdfbox:pdfbox:2.0.29'
    implementation 'org.apache.commons:commons-imaging:1.0-alpha3'
    implementation 'org.apache.xmlgraphics:batik-transcoder:1.17'
    implementation 'org.apache.xmlgraphics:batik-codec:1.17'
    // ZXing Core (SVG 생성을 위해 직접 사용)
    implementation 'com.google.zxing:core:3.5.3'
    implementation 'org.jfree:jfreesvg:3.4.3'
	// JNanoid(qr uuid 생성)
	implementation 'com.aventrix.jnanoid:jnanoid:2.0.0'
	//Mail
	implementation 'org.springframework.boot:spring-boot-starter-mail'
	// Apache POI for Excel processing
	implementation 'org.apache.poi:poi:5.2.5'
	implementation 'org.apache.poi:poi-ooxml:5.2.5'
}

tasks.named('test') {
	useJUnitPlatform()
}
task runDev(type: JavaExec) {
	group = 'application'
	main = 'kr.wayplus.wayplus_qr.WayplusQrApplication'
	classpath = sourceSets.main.runtimeClasspath
	args = ['--spring.profiles.active=dev']
}