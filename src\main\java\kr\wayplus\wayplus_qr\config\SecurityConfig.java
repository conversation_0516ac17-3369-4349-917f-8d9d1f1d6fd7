package kr.wayplus.wayplus_qr.config;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.security.access.hierarchicalroles.RoleHierarchy;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import kr.wayplus.wayplus_qr.config.jwt.JwtAuthenticationEntryPoint;
import kr.wayplus.wayplus_qr.config.jwt.JwtAuthenticationFilter;
import kr.wayplus.wayplus_qr.util.JwtUtil;
import lombok.RequiredArgsConstructor;

@Configuration
@EnableWebSecurity // Spring Security 활성화
@EnableMethodSecurity // @PreAuthorize, @PostAuthorize 어노테이션 사용 활성화 (선택 사항)
@RequiredArgsConstructor
public class SecurityConfig {

    private final JwtUtil jwtUtil;
    private final UserDetailsService userDetailsService;
    private final JwtAuthenticationEntryPoint jwtAuthenticationEntryPoint; // 주입

    /**
     * 비밀번호 암호화 방식을 정의합니다. BCrypt 사용.
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    /**
     * AuthenticationManager 빈을 생성합니다.
     * Spring Security 6 이상에서는 AuthenticationConfiguration을 통해 얻어옵니다.
     */
    @Bean
    public AuthenticationManager authenticationManager(AuthenticationConfiguration authenticationConfiguration) throws Exception {
        return authenticationConfiguration.getAuthenticationManager();
    }

    @Bean
	static RoleHierarchy roleHierarchy() {
		return new RoleHierarchy() {
			@Override
			public Collection<? extends GrantedAuthority> getReachableGrantedAuthorities(Collection<? extends GrantedAuthority> authorities) {
				Map<String, List<String>> hierarchy = new HashMap<>();
				hierarchy.put("SUPER_ADMIN", List.of("PROJECT_ADMIN"));
				hierarchy.put("PROJECT_ADMIN", List.of("SUB_ADMIN"));
				hierarchy.put("SUB_ADMIN", List.of("VIEWER"));

				List<GrantedAuthority> reachableAuthorities = new ArrayList<>();
				for (GrantedAuthority authority : authorities) {
					String role = authority.getAuthority();
					reachableAuthorities.add(authority);
					List<String> reachableRoles = hierarchy.get(role);
					if (reachableRoles != null) {
						for (String reachableRole : reachableRoles) {
							reachableAuthorities.add(new SimpleGrantedAuthority(reachableRole));
						}
					}
				}
				return reachableAuthorities;
			}
		};
	}

    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        // CorsConfiguration configuration = new CorsConfiguration();
        // // 모든 오리진 패턴 허용 (진단 목적)
        // configuration.setAllowedOriginPatterns(List.of("*"));
        // configuration.setAllowedMethods(List.of("GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"));
        // configuration.setAllowedHeaders(List.of("*"));
        // // 자격 증명 허용 (주의: '*' 오리진과 함께 사용할 때는 보안상 신중해야 함)
        // configuration.setAllowCredentials(true);

        // UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        // // 모든 경로에 적용
        // source.registerCorsConfiguration("/**", configuration);
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        // 1) 공개 API: 모든 오리진 허용, 자격증명 OFF
        CorsConfiguration publicCors = new CorsConfiguration();
        publicCors.setAllowedOriginPatterns(List.of("*"));
        publicCors.setAllowedMethods(List.of("GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"));
        publicCors.setAllowedHeaders(List.of("*"));
        publicCors.setAllowCredentials(false);
        source.registerCorsConfiguration("/api/way/public/**", publicCors);

        // 2) 나머지 API: 신뢰된 오리진만 허용, 자격증명 ON
        CorsConfiguration authCors = new CorsConfiguration();
        authCors.setAllowedOrigins(List.of("http://*************:9998", "http://localhost:9998"));
        authCors.setAllowedMethods(List.of("GET","POST","PUT","PATCH","DELETE","OPTIONS"));
        authCors.setAllowedHeaders(List.of("*"));
        authCors.setAllowCredentials(true);
        source.registerCorsConfiguration("/**", authCors);
        return source;

        
    }

    /**
     * Spring Security 필터 체인을 설정합니다.
     */
    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        http
                // CORS 설정 추가 (CorsConfigurationSource 빈 사용)
                .cors(cors -> cors.configurationSource(corsConfigurationSource()))
                // CSRF 보호 비활성화 (Stateless API는 일반적으로 비활성화)
                .csrf(AbstractHttpConfigurer::disable)

                // HTTP Basic 인증 비활성화
                .httpBasic(AbstractHttpConfigurer::disable)

                // 폼 로그인 비활성화
                .formLogin(AbstractHttpConfigurer::disable)

                // 세션 관리를 STATELESS로 설정 (JWT 사용 시 필수)
                .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))

                 // 인증/인가 예외 처리 설정 추가
                .exceptionHandling(exceptions ->
                        exceptions.authenticationEntryPoint(jwtAuthenticationEntryPoint) // 인증 실패 시 (401)
                )

                // 요청 경로별 인가 설정
                .authorizeHttpRequests(authorize -> authorize
                        .requestMatchers("/api/way/public/**").permitAll() // 공개 랜딩 페이지 조회는 인증 없이 허용
                        .requestMatchers("/qr-images/**").permitAll() // 저장된 QR 이미지 접근 허용
                        .requestMatchers(
                                "/api/way/auth/login",
                                "/api/way/auth/refresh",
                                "/api/way/auth/change-initial-password" // 초기 비밀번호 변경 API 허용
                        ).permitAll() // 로그인, 토큰 갱신, 초기 비밀번호 변경 허용
                        .requestMatchers("/api/way/qr/redirect/**").permitAll() // QR 코드 리디렉션 엔드포인트는 인증 없이 허용
                        .requestMatchers(HttpMethod.GET, "/api/way/projects/**").authenticated() // GET /api/way/projects/** 경로는 인증 필요 (프로젝트 조회)
                        .requestMatchers("/api/way/super/menus/**").hasRole("SUPER_ADMIN") // SUPER_ADMIN 전용 메뉴 관리 API
                        .requestMatchers("/api/way/menus/**").hasAnyRole("SUPER_ADMIN", "PROJECT_ADMIN", "SUB_ADMIN", "VIEWER") // 일반 메뉴 접근 API
                        .requestMatchers("/api/way/**").authenticated() // /api/way/ 하위의 다른 모든 경로는 인증 필요 (로그아웃 포함)
                        .anyRequest().permitAll() // 그 외 요청(예: /, /swagger-ui/** 등)은 일단 모두 허용 (필요에 따라 수정)
                )

                .addFilterBefore(new JwtAuthenticationFilter(jwtUtil, userDetailsService), UsernamePasswordAuthenticationFilter.class);


        return http.build();
    }
}
