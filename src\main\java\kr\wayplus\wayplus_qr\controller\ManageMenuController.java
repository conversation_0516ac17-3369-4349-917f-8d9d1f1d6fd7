package kr.wayplus.wayplus_qr.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import kr.wayplus.wayplus_qr.dto.MenuDto;
import kr.wayplus.wayplus_qr.dto.response.ApiResponseDto;
import kr.wayplus.wayplus_qr.entity.User;
import kr.wayplus.wayplus_qr.service.ManageMenuService;
import kr.wayplus.wayplus_qr.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/way/manage/menus")
@RequiredArgsConstructor
@Tag(name = "Menu Management", description = "메뉴 관리 API")
public class ManageMenuController {

    private final ManageMenuService manageMenuService;
    private final UserService userService;

    // ========== SUPER_ADMIN 전용 메뉴 관리 API ==========

    @Operation(summary = "메뉴 생성", description = "새로운 메뉴를 생성합니다. (SUPER_ADMIN 전용)")
    @PostMapping
    @PreAuthorize("hasAuthority('SUPER_ADMIN')")
    public ResponseEntity<ApiResponseDto<Long>> createMenu(
            @Valid @RequestBody MenuDto.CreateRequest requestDto,
            Authentication authentication) {
        
        log.info("Creating menu: {} by user: {}", requestDto.getMenuCode(), authentication.getName());
        ApiResponseDto<Long> response = manageMenuService.createMenu(requestDto, authentication.getName());
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "메뉴 수정", description = "기존 메뉴 정보를 수정합니다. (SUPER_ADMIN 전용)")
    @PutMapping("/{menuId}")
    @PreAuthorize("hasAuthority('SUPER_ADMIN')")
    public ResponseEntity<ApiResponseDto<Void>> updateMenu(
            @Parameter(description = "메뉴 ID", required = true) @PathVariable Long menuId,
            @Valid @RequestBody MenuDto.UpdateRequest requestDto,
            Authentication authentication) {
        
        log.info("Updating menu ID: {} by user: {}", menuId, authentication.getName());
        ApiResponseDto<Void> response = manageMenuService.updateMenu(menuId, requestDto, authentication.getName());
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "메뉴 삭제", description = "메뉴를 삭제합니다. (SUPER_ADMIN 전용)")
    @DeleteMapping("/{menuId}")
    @PreAuthorize("hasAuthority('SUPER_ADMIN')")
    public ResponseEntity<ApiResponseDto<Void>> deleteMenu(
            @Parameter(description = "메뉴 ID", required = true) @PathVariable Long menuId,
            Authentication authentication) {
        
        log.info("Deleting menu ID: {} by user: {}", menuId, authentication.getName());
        ApiResponseDto<Void> response = manageMenuService.deleteMenu(menuId, authentication.getName());
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "메뉴 트리 조회", description = "전체 메뉴 트리 구조를 조회합니다. (SUPER_ADMIN 전용)")
    @GetMapping("/tree")
    @PreAuthorize("hasAuthority('SUPER_ADMIN')")
    public ResponseEntity<ApiResponseDto<List<MenuDto.Response>>> getMenuTree() {
        log.info("Getting menu tree");
        ApiResponseDto<List<MenuDto.Response>> response = manageMenuService.getMenuTree();
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "메뉴 역할 권한 설정", description = "특정 메뉴에 대한 역할별 접근 권한을 설정합니다. (SUPER_ADMIN 전용)")
    @PostMapping("/{menuId}/roles/{roleId}/permissions")
    @PreAuthorize("hasAuthority('SUPER_ADMIN')")
    public ResponseEntity<ApiResponseDto<Void>> setMenuRolePermission(
            @Parameter(description = "메뉴 ID", required = true) @PathVariable Long menuId,
            @Parameter(description = "역할 ID", required = true) @PathVariable String roleId,
            @Parameter(description = "접근 가능 여부 (Y/N)", required = true) @RequestParam String isAccessible,
            Authentication authentication) {
        
        log.info("Setting menu role permission - Menu: {}, Role: {}, Accessible: {}", menuId, roleId, isAccessible);
        ApiResponseDto<Void> response = manageMenuService.setMenuRolePermission(menuId, roleId, isAccessible, authentication.getName());
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "메뉴 사용자 권한 설정", description = "특정 메뉴에 대한 개별 사용자 접근 권한을 설정합니다. (SUPER_ADMIN 전용)")
    @PostMapping("/{menuId}/users/{userEmail}/permissions")
    @PreAuthorize("hasAuthority('SUPER_ADMIN')")
    public ResponseEntity<ApiResponseDto<Void>> setMenuUserPermission(
            @Parameter(description = "메뉴 ID", required = true) @PathVariable Long menuId,
            @Parameter(description = "사용자 이메일", required = true) @PathVariable String userEmail,
            @Valid @RequestBody MenuDto.UserPermissionRequest requestDto,
            Authentication authentication) {
        
        // PathVariable의 값으로 DTO 업데이트
        MenuDto.UserPermissionRequest updatedRequestDto = MenuDto.UserPermissionRequest.builder()
                .menuId(menuId)
                .userEmail(userEmail)
                .isAccessible(requestDto.getIsAccessible())
                .permissionNote(requestDto.getPermissionNote())
                .build();
        
        log.info("Setting menu user permission - Menu: {}, User: {}", menuId, userEmail);
        ApiResponseDto<Void> response = manageMenuService.setMenuUserPermission(updatedRequestDto, authentication.getName());
        return ResponseEntity.ok(response);
    }

    // ========== 일반 사용자용 메뉴 접근 API ==========

    @Operation(summary = "접근 가능한 메뉴 트리 조회", description = "현재 로그인한 사용자가 접근 가능한 메뉴 트리를 조회합니다.")
    @GetMapping("/accessible")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'PROJECT_ADMIN', 'SUB_ADMIN', 'VIEWER')")
    public ResponseEntity<ApiResponseDto<List<MenuDto.Response>>> getAccessibleMenuTree(
            Authentication authentication) {
        
        String userEmail = authentication.getName();
        log.info("Getting accessible menu tree for user: {}", userEmail);
        
        // 사용자 정보 조회하여 역할 확인
        User user = userService.getUserByUserEmail(userEmail);
        String roleId = user.getRoleId();
        
        ApiResponseDto<List<MenuDto.Response>> response = manageMenuService.getAccessibleMenuTree(userEmail, roleId);
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "메뉴 접근 권한 확인", description = "특정 메뉴에 대한 현재 사용자의 접근 권한을 확인합니다.")
    @GetMapping("/{menuId}/access")
    @PreAuthorize("hasAnyRole('SUPER_ADMIN', 'PROJECT_ADMIN', 'SUB_ADMIN', 'VIEWER')")
    public ResponseEntity<ApiResponseDto<Boolean>> checkMenuAccess(
            @Parameter(description = "메뉴 ID", required = true) @PathVariable Long menuId,
            Authentication authentication) {
        
        String userEmail = authentication.getName();
        log.info("Checking menu access for user: {} on menu: {}", userEmail, menuId);
        
        // 사용자 정보 조회하여 역할 확인
        User user = userService.getUserByUserEmail(userEmail);
        String roleId = user.getRoleId();
        
        // 메뉴 접근 권한 확인
        boolean hasAccess = manageMenuService.hasMenuAccess(menuId, userEmail, roleId);
        
        ApiResponseDto<Boolean> response = ApiResponseDto.success(hasAccess);
        return ResponseEntity.ok(response);
    }
}
