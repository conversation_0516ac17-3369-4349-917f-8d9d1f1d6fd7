package kr.wayplus.wayplus_qr.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import kr.wayplus.wayplus_qr.dto.response.ApiResponseDto;
import kr.wayplus.wayplus_qr.dto.response.MenuTreeResponseDto;
import kr.wayplus.wayplus_qr.entity.User;
import kr.wayplus.wayplus_qr.service.MenuPermissionService;
import kr.wayplus.wayplus_qr.service.MenuService;
import kr.wayplus.wayplus_qr.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/way/menus")
@RequiredArgsConstructor
@Tag(name = "Menu Access", description = "사용자별 메뉴 접근 API")
@PreAuthorize("hasAnyRole('SUPER_ADMIN', 'PROJECT_ADMIN', 'SUB_ADMIN', 'VIEWER')")
public class MenuController {

    private final MenuService menuService;
    private final MenuPermissionService menuPermissionService;
    private final UserService userService;

    @Operation(summary = "접근 가능한 메뉴 트리 조회", description = "현재 로그인한 사용자가 접근 가능한 메뉴 트리를 조회합니다.")
    @GetMapping("/accessible")
    public ResponseEntity<ApiResponseDto<List<MenuTreeResponseDto>>> getAccessibleMenuTree(
            Authentication authentication) {
        
        String userEmail = authentication.getName();
        log.info("Getting accessible menu tree for user: {}", userEmail);
        
        // 사용자 정보 조회하여 역할 확인
        User user = userService.getUserByUserEmail(userEmail);
        String roleId = user.getRoleId();
        
        ApiResponseDto<List<MenuTreeResponseDto>> response = menuService.getAccessibleMenuTree(userEmail, roleId);
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "메뉴 접근 권한 확인", description = "특정 메뉴에 대한 현재 사용자의 접근 권한을 확인합니다.")
    @GetMapping("/{menuId}/access")
    public ResponseEntity<ApiResponseDto<Boolean>> checkMenuAccess(
            @Parameter(description = "메뉴 ID", required = true) @PathVariable Long menuId,
            Authentication authentication) {
        
        String userEmail = authentication.getName();
        log.info("Checking menu access for user: {} on menu: {}", userEmail, menuId);
        
        // 사용자 정보 조회하여 역할 확인
        User user = userService.getUserByUserEmail(userEmail);
        String roleId = user.getRoleId();

        // 메뉴 접근 권한 확인
        boolean hasAccess = menuPermissionService.hasMenuAccess(menuId, userEmail, roleId);

        ApiResponseDto<Boolean> response = ApiResponseDto.success(hasAccess);
        return ResponseEntity.ok(response);
    }
}
