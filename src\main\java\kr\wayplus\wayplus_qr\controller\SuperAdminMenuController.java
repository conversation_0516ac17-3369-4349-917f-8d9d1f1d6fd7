package kr.wayplus.wayplus_qr.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import kr.wayplus.wayplus_qr.dto.request.MenuCreateRequestDto;
import kr.wayplus.wayplus_qr.dto.request.MenuRolePermissionRequestDto;
import kr.wayplus.wayplus_qr.dto.request.MenuUpdateRequestDto;
import kr.wayplus.wayplus_qr.dto.request.MenuUserPermissionRequestDto;
import kr.wayplus.wayplus_qr.dto.response.ApiResponseDto;
import kr.wayplus.wayplus_qr.dto.response.MenuPermissionResponseDto;
import kr.wayplus.wayplus_qr.dto.response.MenuResponseDto;
import kr.wayplus.wayplus_qr.dto.response.MenuTreeResponseDto;
import kr.wayplus.wayplus_qr.service.MenuPermissionService;
import kr.wayplus.wayplus_qr.service.MenuService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/way/super/menus")
@RequiredArgsConstructor
@Tag(name = "Super Admin Menu Management", description = "SUPER_ADMIN 전용 메뉴 관리 API")
@PreAuthorize("hasRole('SUPER_ADMIN')")
public class SuperAdminMenuController {

    private final MenuService menuService;
    private final MenuPermissionService menuPermissionService;

    @Operation(summary = "메뉴 생성", description = "새로운 메뉴를 생성합니다. (SUPER_ADMIN 전용)")
    @PostMapping
    public ResponseEntity<ApiResponseDto<Long>> createMenu(
            @Valid @RequestBody MenuCreateRequestDto requestDto,
            Authentication authentication) {
        
        log.info("Creating menu: {} by user: {}", requestDto.getMenuCode(), authentication.getName());
        ApiResponseDto<Long> response = menuService.createMenu(requestDto, authentication.getName());
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "메뉴 수정", description = "기존 메뉴 정보를 수정합니다. (SUPER_ADMIN 전용)")
    @PutMapping("/{menuId}")
    public ResponseEntity<ApiResponseDto<Void>> updateMenu(
            @Parameter(description = "메뉴 ID", required = true) @PathVariable Long menuId,
            @Valid @RequestBody MenuUpdateRequestDto requestDto,
            Authentication authentication) {
        
        log.info("Updating menu ID: {} by user: {}", menuId, authentication.getName());
        ApiResponseDto<Void> response = menuService.updateMenu(menuId, requestDto, authentication.getName());
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "메뉴 삭제", description = "메뉴를 삭제합니다. 하위 메뉴가 있는 경우 삭제할 수 없습니다. (SUPER_ADMIN 전용)")
    @DeleteMapping("/{menuId}")
    public ResponseEntity<ApiResponseDto<Void>> deleteMenu(
            @Parameter(description = "메뉴 ID", required = true) @PathVariable Long menuId,
            Authentication authentication) {
        
        log.info("Deleting menu ID: {} by user: {}", menuId, authentication.getName());
        ApiResponseDto<Void> response = menuService.deleteMenu(menuId, authentication.getName());
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "메뉴 상세 조회", description = "메뉴 ID로 메뉴 상세 정보를 조회합니다. (SUPER_ADMIN 전용)")
    @GetMapping("/{menuId}")
    public ResponseEntity<ApiResponseDto<MenuResponseDto>> getMenuById(
            @Parameter(description = "메뉴 ID", required = true) @PathVariable Long menuId) {
        
        log.info("Getting menu by ID: {}", menuId);
        ApiResponseDto<MenuResponseDto> response = menuService.getMenuById(menuId);
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "모든 메뉴 목록 조회", description = "모든 활성 메뉴 목록을 조회합니다. (SUPER_ADMIN 전용)")
    @GetMapping
    public ResponseEntity<ApiResponseDto<List<MenuResponseDto>>> getAllActiveMenus() {
        log.info("Getting all active menus");
        ApiResponseDto<List<MenuResponseDto>> response = menuService.getAllActiveMenus();
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "메뉴 트리 조회", description = "계층형 메뉴 트리 구조를 조회합니다. (SUPER_ADMIN 전용)")
    @GetMapping("/tree")
    public ResponseEntity<ApiResponseDto<List<MenuTreeResponseDto>>> getMenuTree() {
        log.info("Getting menu tree");
        ApiResponseDto<List<MenuTreeResponseDto>> response = menuService.getMenuTree();
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "메뉴 권한 설정 - 역할별", description = "특정 메뉴에 대한 역할별 접근 권한을 설정합니다. (SUPER_ADMIN 전용)")
    @PostMapping("/{menuId}/roles/{roleId}/permissions")
    public ResponseEntity<ApiResponseDto<Void>> setMenuRolePermission(
            @Parameter(description = "메뉴 ID", required = true) @PathVariable Long menuId,
            @Parameter(description = "역할 ID", required = true) @PathVariable String roleId,
            @Parameter(description = "접근 가능 여부 (Y/N)", required = true) @RequestParam String isAccessible,
            Authentication authentication) {
        
        MenuRolePermissionRequestDto requestDto = MenuRolePermissionRequestDto.builder()
                .menuId(menuId)
                .roleId(roleId)
                .isAccessible(isAccessible)
                .build();
        
        log.info("Setting menu role permission - Menu: {}, Role: {}, Accessible: {}", menuId, roleId, isAccessible);
        ApiResponseDto<Void> response = menuPermissionService.setMenuRolePermission(requestDto, authentication.getName());
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "메뉴 권한 설정 - 사용자별", description = "특정 메뉴에 대한 개별 사용자 접근 권한을 설정합니다. (SUPER_ADMIN 전용)")
    @PostMapping("/{menuId}/users/{userEmail}/permissions")
    public ResponseEntity<ApiResponseDto<Void>> setMenuUserPermission(
            @Parameter(description = "메뉴 ID", required = true) @PathVariable Long menuId,
            @Parameter(description = "사용자 이메일", required = true) @PathVariable String userEmail,
            @Valid @RequestBody MenuUserPermissionRequestDto requestDto,
            Authentication authentication) {
        
        // PathVariable의 값으로 DTO 업데이트
        MenuUserPermissionRequestDto updatedRequestDto = MenuUserPermissionRequestDto.builder()
                .menuId(menuId)
                .userEmail(userEmail)
                .isAccessible(requestDto.getIsAccessible())
                .permissionNote(requestDto.getPermissionNote())
                .build();
        
        log.info("Setting menu user permission - Menu: {}, User: {}, Accessible: {}", menuId, userEmail, requestDto.getIsAccessible());
        ApiResponseDto<Void> response = menuPermissionService.setMenuUserPermission(updatedRequestDto, authentication.getName());
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "메뉴 권한 조회", description = "특정 메뉴의 모든 권한 정보(역할별, 사용자별)를 조회합니다. (SUPER_ADMIN 전용)")
    @GetMapping("/{menuId}/permissions")
    public ResponseEntity<ApiResponseDto<MenuPermissionResponseDto>> getMenuPermissions(
            @Parameter(description = "메뉴 ID", required = true) @PathVariable Long menuId) {
        
        log.info("Getting menu permissions for menu ID: {}", menuId);
        ApiResponseDto<MenuPermissionResponseDto> response = menuPermissionService.getMenuPermissions(menuId);
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "메뉴 역할 권한 삭제", description = "특정 메뉴의 역할별 접근 권한을 삭제합니다. (SUPER_ADMIN 전용)")
    @DeleteMapping("/{menuId}/roles/{roleId}/permissions")
    public ResponseEntity<ApiResponseDto<Void>> deleteMenuRolePermission(
            @Parameter(description = "메뉴 ID", required = true) @PathVariable Long menuId,
            @Parameter(description = "역할 ID", required = true) @PathVariable String roleId,
            Authentication authentication) {
        
        log.info("Deleting menu role permission - Menu: {}, Role: {}", menuId, roleId);
        ApiResponseDto<Void> response = menuPermissionService.deleteMenuRolePermission(menuId, roleId, authentication.getName());
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "메뉴 사용자 권한 삭제", description = "특정 메뉴의 개별 사용자 접근 권한을 삭제합니다. (SUPER_ADMIN 전용)")
    @DeleteMapping("/{menuId}/users/{userEmail}/permissions")
    public ResponseEntity<ApiResponseDto<Void>> deleteMenuUserPermission(
            @Parameter(description = "메뉴 ID", required = true) @PathVariable Long menuId,
            @Parameter(description = "사용자 이메일", required = true) @PathVariable String userEmail,
            Authentication authentication) {
        
        log.info("Deleting menu user permission - Menu: {}, User: {}", menuId, userEmail);
        ApiResponseDto<Void> response = menuPermissionService.deleteMenuUserPermission(menuId, userEmail, authentication.getName());
        return ResponseEntity.ok(response);
    }
}
