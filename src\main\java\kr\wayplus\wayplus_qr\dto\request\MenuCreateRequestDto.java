package kr.wayplus.wayplus_qr.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import kr.wayplus.wayplus_qr.entity.Menu;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "메뉴 생성 요청 DTO")
public class MenuCreateRequestDto {

    @Schema(description = "상위 메뉴 ID (최상위 메뉴인 경우 null)", example = "1")
    private Long parentMenuId;

    @NotBlank(message = "메뉴 코드는 필수입니다.")
    @Size(max = 100, message = "메뉴 코드는 100자를 초과할 수 없습니다.")
    @Pattern(regexp = "^[A-Z0-9_]+$", message = "메뉴 코드는 영문 대문자, 숫자, 언더스코어만 사용 가능합니다.")
    @Schema(description = "메뉴 코드 (영문 대문자, 숫자, 언더스코어)", example = "QR_MANAGEMENT")
    private String menuCode;

    @NotBlank(message = "메뉴 이름은 필수입니다.")
    @Size(max = 255, message = "메뉴 이름은 255자를 초과할 수 없습니다.")
    @Schema(description = "메뉴 이름", example = "QR 관리")
    private String menuName;

    @Size(max = 500, message = "메뉴 URL은 500자를 초과할 수 없습니다.")
    @Schema(description = "메뉴 URL 경로", example = "/admin/qr")
    private String menuUrl;

    @Size(max = 100, message = "메뉴 아이콘은 100자를 초과할 수 없습니다.")
    @Schema(description = "메뉴 아이콘 클래스명", example = "fas fa-qrcode")
    private String menuIcon;

    @NotNull(message = "메뉴 레벨은 필수입니다.")
    @Schema(description = "메뉴 레벨 (1: 최상위, 2: 2단계, 3: 3단계)", example = "1")
    private Integer menuLevel;

    @Schema(description = "메뉴 표시 순서", example = "1")
    private Integer displayOrder;

    @Pattern(regexp = "^(ACTIVE|INACTIVE)$", message = "메뉴 상태는 ACTIVE 또는 INACTIVE만 가능합니다.")
    @Schema(description = "메뉴 상태 (ACTIVE: 활성화, INACTIVE: 비활성화)", example = "ACTIVE")
    private String status;

    /**
     * DTO를 Entity로 변환
     */
    public Menu toEntity(String createUserEmail) {
        return Menu.builder()
                .parentMenuId(this.parentMenuId)
                .menuCode(this.menuCode)
                .menuName(this.menuName)
                .menuUrl(this.menuUrl)
                .menuIcon(this.menuIcon)
                .menuLevel(this.menuLevel)
                .displayOrder(this.displayOrder != null ? this.displayOrder : 0)
                .status(this.status != null ? this.status : "ACTIVE")
                .createUserEmail(createUserEmail)
                .updateUserEmail(createUserEmail)
                .createDate(LocalDateTime.now())
                .lastUpdateDate(LocalDateTime.now())
                .useYn("Y")
                .deleteYn("N")
                .build();
    }
}
