package kr.wayplus.wayplus_qr.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.List;

@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "메뉴 역할 권한 설정 요청 DTO")
public class MenuRolePermissionRequestDto {

    @NotNull(message = "메뉴 ID는 필수입니다.")
    @Schema(description = "메뉴 ID", example = "1")
    private Long menuId;

    @NotBlank(message = "역할 ID는 필수입니다.")
    @Schema(description = "역할 ID", example = "PROJECT_ADMIN")
    private String roleId;

    @NotBlank(message = "접근 가능 여부는 필수입니다.")
    @Pattern(regexp = "^(Y|N)$", message = "접근 가능 여부는 Y 또는 N만 가능합니다.")
    @Schema(description = "접근 가능 여부 (Y: 가능, N: 불가능)", example = "Y")
    private String isAccessible;
}

@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "메뉴 역할 권한 일괄 설정 요청 DTO")
class MenuRolePermissionBatchRequestDto {

    @NotNull(message = "메뉴 ID는 필수입니다.")
    @Schema(description = "메뉴 ID", example = "1")
    private Long menuId;

    @Schema(description = "역할별 권한 설정 목록")
    private List<RolePermissionDto> rolePermissions;

    @Getter
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "역할별 권한 설정")
    public static class RolePermissionDto {
        @NotBlank(message = "역할 ID는 필수입니다.")
        @Schema(description = "역할 ID", example = "PROJECT_ADMIN")
        private String roleId;

        @NotBlank(message = "접근 가능 여부는 필수입니다.")
        @Pattern(regexp = "^(Y|N)$", message = "접근 가능 여부는 Y 또는 N만 가능합니다.")
        @Schema(description = "접근 가능 여부 (Y: 가능, N: 불가능)", example = "Y")
        private String isAccessible;
    }
}
