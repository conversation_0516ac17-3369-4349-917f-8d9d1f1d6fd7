package kr.wayplus.wayplus_qr.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import kr.wayplus.wayplus_qr.entity.MenuUserPermission;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "메뉴 사용자 권한 설정 요청 DTO")
public class MenuUserPermissionRequestDto {

    @NotNull(message = "메뉴 ID는 필수입니다.")
    @Schema(description = "메뉴 ID", example = "1")
    private Long menuId;

    @NotBlank(message = "사용자 이메일은 필수입니다.")
    @Email(message = "올바른 이메일 형식이어야 합니다.")
    @Schema(description = "사용자 이메일", example = "<EMAIL>")
    private String userEmail;

    @NotBlank(message = "접근 가능 여부는 필수입니다.")
    @Pattern(regexp = "^(Y|N)$", message = "접근 가능 여부는 Y 또는 N만 가능합니다.")
    @Schema(description = "접근 가능 여부 (Y: 가능, N: 불가능)", example = "Y")
    private String isAccessible;

    @Size(max = 1000, message = "권한 부여 사유는 1000자를 초과할 수 없습니다.")
    @Schema(description = "권한 부여 사유 또는 메모", example = "특별 권한 부여")
    private String permissionNote;

    /**
     * DTO를 Entity로 변환
     */
    public MenuUserPermission toEntity(String createUserEmail) {
        return MenuUserPermission.builder()
                .menuId(this.menuId)
                .userEmail(this.userEmail)
                .isAccessible(this.isAccessible)
                .permissionNote(this.permissionNote)
                .createUserEmail(createUserEmail)
                .updateUserEmail(createUserEmail)
                .createDate(LocalDateTime.now())
                .lastUpdateDate(LocalDateTime.now())
                .useYn("Y")
                .deleteYn("N")
                .build();
    }
}
