package kr.wayplus.wayplus_qr.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "메뉴 권한 응답 DTO")
public class MenuPermissionResponseDto {

    @Schema(description = "메뉴 ID", example = "1")
    private Long menuId;

    @Schema(description = "메뉴 이름", example = "QR 관리")
    private String menuName;

    @Schema(description = "메뉴 코드", example = "QR_MANAGEMENT")
    private String menuCode;

    @Schema(description = "역할별 권한 목록")
    private List<RolePermissionDto> rolePermissions;

    @Schema(description = "사용자별 권한 목록")
    private List<UserPermissionDto> userPermissions;

    @Getter
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "역할별 권한 정보")
    public static class RolePermissionDto {
        @Schema(description = "권한 ID", example = "1")
        private Long permissionId;

        @Schema(description = "역할 ID", example = "PROJECT_ADMIN")
        private String roleId;

        @Schema(description = "역할 이름", example = "프로젝트 관리자")
        private String roleName;

        @Schema(description = "접근 가능 여부", example = "Y")
        private String isAccessible;

        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Seoul")
        @Schema(description = "생성일시", example = "2024-01-01 10:00:00")
        private LocalDateTime createDate;

        @Schema(description = "생성자 이메일", example = "<EMAIL>")
        private String createUserEmail;
    }

    @Getter
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "사용자별 권한 정보")
    public static class UserPermissionDto {
        @Schema(description = "사용자 권한 ID", example = "1")
        private Long userPermissionId;

        @Schema(description = "사용자 이메일", example = "<EMAIL>")
        private String userEmail;

        @Schema(description = "사용자 이름", example = "홍길동")
        private String userName;

        @Schema(description = "접근 가능 여부", example = "Y")
        private String isAccessible;

        @Schema(description = "권한 부여 사유", example = "특별 권한 부여")
        private String permissionNote;

        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Seoul")
        @Schema(description = "생성일시", example = "2024-01-01 10:00:00")
        private LocalDateTime createDate;

        @Schema(description = "생성자 이메일", example = "<EMAIL>")
        private String createUserEmail;
    }
}
