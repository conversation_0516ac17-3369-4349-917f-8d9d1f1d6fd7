package kr.wayplus.wayplus_qr.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "메뉴 응답 DTO")
public class MenuResponseDto {

    @Schema(description = "메뉴 ID", example = "1")
    private Long menuId;

    @Schema(description = "상위 메뉴 ID", example = "null")
    private Long parentMenuId;

    @Schema(description = "메뉴 코드", example = "QR_MANAGEMENT")
    private String menuCode;

    @Schema(description = "메뉴 이름", example = "QR 관리")
    private String menuName;

    @Schema(description = "메뉴 URL", example = "/admin/qr")
    private String menuUrl;

    @Schema(description = "메뉴 아이콘", example = "fas fa-qrcode")
    private String menuIcon;

    @Schema(description = "메뉴 레벨", example = "1")
    private Integer menuLevel;

    @Schema(description = "표시 순서", example = "1")
    private Integer displayOrder;

    @Schema(description = "메뉴 상태", example = "ACTIVE")
    private String status;

    @Schema(description = "생성자 이메일", example = "<EMAIL>")
    private String createUserEmail;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Seoul")
    @Schema(description = "생성일시", example = "2024-01-01 10:00:00")
    private LocalDateTime createDate;

    @Schema(description = "수정자 이메일", example = "<EMAIL>")
    private String updateUserEmail;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Seoul")
    @Schema(description = "수정일시", example = "2024-01-01 10:00:00")
    private LocalDateTime lastUpdateDate;

    @Schema(description = "하위 메뉴 목록")
    private List<MenuResponseDto> children;

    @Schema(description = "접근 가능한 역할 목록")
    private List<String> accessibleRoles;
}
