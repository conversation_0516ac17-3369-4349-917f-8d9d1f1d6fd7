package kr.wayplus.wayplus_qr.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.List;

@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "메뉴 트리 구조 응답 DTO")
public class MenuTreeResponseDto {

    @Schema(description = "메뉴 ID", example = "1")
    private Long menuId;

    @Schema(description = "상위 메뉴 ID", example = "null")
    private Long parentMenuId;

    @Schema(description = "메뉴 코드", example = "QR_MANAGEMENT")
    private String menuCode;

    @Schema(description = "메뉴 이름", example = "QR 관리")
    private String menuName;

    @Schema(description = "메뉴 URL", example = "/admin/qr")
    private String menuUrl;

    @Schema(description = "메뉴 아이콘", example = "fas fa-qrcode")
    private String menuIcon;

    @Schema(description = "메뉴 레벨", example = "1")
    private Integer menuLevel;

    @Schema(description = "표시 순서", example = "1")
    private Integer displayOrder;

    @Schema(description = "메뉴 상태", example = "ACTIVE")
    private String status;

    @Schema(description = "하위 메뉴 목록")
    private List<MenuTreeResponseDto> children;

    @Schema(description = "접근 가능 여부", example = "true")
    private Boolean accessible;
}
