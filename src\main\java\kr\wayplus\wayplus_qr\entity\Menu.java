package kr.wayplus.wayplus_qr.entity;

import lombok.*;

import java.time.LocalDateTime;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Menu {
    private Long menuId;
    private Long parentMenuId;
    private String menuCode;
    private String menuName;
    private String menuUrl;
    private String menuIcon;
    private Integer menuLevel;
    private Integer displayOrder;
    private String status; // ACTIVE, INACTIVE
    private String createUserEmail;
    private LocalDateTime createDate;
    private String updateUserEmail;
    private LocalDateTime lastUpdateDate;
    private String deleteUserEmail;
    private LocalDateTime deleteDate;
    private String useYn;
    private String deleteYn;
}
