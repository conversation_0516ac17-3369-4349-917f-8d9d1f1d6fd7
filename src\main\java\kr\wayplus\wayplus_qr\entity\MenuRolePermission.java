package kr.wayplus.wayplus_qr.entity;

import lombok.*;

import java.time.LocalDateTime;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MenuRolePermission {
    private Long permissionId;
    private Long menuId;
    private String roleId;
    private String isAccessible; // Y, N
    private String createUserEmail;
    private LocalDateTime createDate;
    private String updateUserEmail;
    private LocalDateTime lastUpdateDate;
    private String deleteUserEmail;
    private LocalDateTime deleteDate;
    private String useYn;
    private String deleteYn;
}
