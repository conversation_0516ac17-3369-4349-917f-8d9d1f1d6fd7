package kr.wayplus.wayplus_qr.mapper;

import kr.wayplus.wayplus_qr.dto.response.MenuResponseDto;
import kr.wayplus.wayplus_qr.dto.response.MenuTreeResponseDto;
import kr.wayplus.wayplus_qr.entity.Menu;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Optional;

@Mapper
public interface MenuMapper {

    /**
     * 메뉴를 생성합니다.
     *
     * @param menu 생성할 메뉴 정보
     * @return 생성된 행 수
     */
    int insertMenu(Menu menu);

    /**
     * 메뉴 정보를 수정합니다.
     *
     * @param menu 수정할 메뉴 정보
     * @return 수정된 행 수
     */
    int updateMenu(Menu menu);

    /**
     * 메뉴를 논리 삭제합니다.
     *
     * @param menuId 삭제할 메뉴 ID
     * @param deleteUserEmail 삭제자 이메일
     * @return 삭제된 행 수
     */
    int deleteMenu(@Param("menuId") Long menuId, @Param("deleteUserEmail") String deleteUserEmail);

    /**
     * 메뉴 ID로 메뉴 정보를 조회합니다.
     *
     * @param menuId 메뉴 ID
     * @return 메뉴 정보
     */
    Optional<Menu> selectMenuById(@Param("menuId") Long menuId);

    /**
     * 메뉴 코드로 메뉴 정보를 조회합니다.
     *
     * @param menuCode 메뉴 코드
     * @return 메뉴 정보
     */
    Optional<Menu> selectMenuByCode(@Param("menuCode") String menuCode);

    /**
     * 모든 활성 메뉴 목록을 조회합니다.
     *
     * @return 메뉴 목록
     */
    List<MenuResponseDto> selectAllActiveMenus();

    /**
     * 계층형 메뉴 트리를 조회합니다.
     *
     * @return 메뉴 트리 목록
     */
    List<MenuTreeResponseDto> selectMenuTree();

    /**
     * 특정 사용자가 접근 가능한 메뉴 트리를 조회합니다.
     *
     * @param userEmail 사용자 이메일
     * @param roleId 사용자 역할 ID
     * @return 접근 가능한 메뉴 트리 목록
     */
    List<MenuTreeResponseDto> selectAccessibleMenuTree(@Param("userEmail") String userEmail, @Param("roleId") String roleId);

    /**
     * 상위 메뉴 ID로 하위 메뉴 목록을 조회합니다.
     *
     * @param parentMenuId 상위 메뉴 ID
     * @return 하위 메뉴 목록
     */
    List<MenuResponseDto> selectMenusByParentId(@Param("parentMenuId") Long parentMenuId);

    /**
     * 메뉴 코드 중복 여부를 확인합니다.
     *
     * @param menuCode 메뉴 코드
     * @param excludeMenuId 제외할 메뉴 ID (수정 시 자기 자신 제외)
     * @return 중복 여부
     */
    boolean existsByMenuCode(@Param("menuCode") String menuCode, @Param("excludeMenuId") Long excludeMenuId);

    /**
     * 특정 메뉴에 하위 메뉴가 있는지 확인합니다.
     *
     * @param menuId 메뉴 ID
     * @return 하위 메뉴 존재 여부
     */
    boolean hasChildMenus(@Param("menuId") Long menuId);

    /**
     * 메뉴 레벨별 최대 표시 순서를 조회합니다.
     *
     * @param parentMenuId 상위 메뉴 ID
     * @param menuLevel 메뉴 레벨
     * @return 최대 표시 순서
     */
    Integer selectMaxDisplayOrder(@Param("parentMenuId") Long parentMenuId, @Param("menuLevel") Integer menuLevel);
}
