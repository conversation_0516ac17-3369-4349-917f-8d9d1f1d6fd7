package kr.wayplus.wayplus_qr.mapper;

import kr.wayplus.wayplus_qr.dto.MenuDto;
import kr.wayplus.wayplus_qr.entity.MenuRolePermission;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Optional;

@Mapper
public interface MenuRolePermissionMapper {

    /**
     * 메뉴 역할 권한을 생성합니다.
     *
     * @param menuRolePermission 생성할 권한 정보
     * @return 생성된 행 수
     */
    int insertMenuRolePermission(MenuRolePermission menuRolePermission);

    /**
     * 메뉴 역할 권한을 수정합니다.
     *
     * @param menuRolePermission 수정할 권한 정보
     * @return 수정된 행 수
     */
    int updateMenuRolePermission(MenuRolePermission menuRolePermission);

    /**
     * 메뉴 역할 권한을 논리 삭제합니다.
     *
     * @param permissionId 권한 ID
     * @param deleteUserEmail 삭제자 이메일
     * @return 삭제된 행 수
     */
    int deleteMenuRolePermission(@Param("permissionId") Long permissionId, @Param("deleteUserEmail") String deleteUserEmail);

    /**
     * 메뉴와 역할로 권한 정보를 조회합니다.
     *
     * @param menuId 메뉴 ID
     * @param roleId 역할 ID
     * @return 권한 정보
     */
    Optional<MenuRolePermission> selectByMenuIdAndRoleId(@Param("menuId") Long menuId, @Param("roleId") String roleId);

    /**
     * 특정 메뉴의 모든 역할 권한을 조회합니다.
     *
     * @param menuId 메뉴 ID
     * @return 역할 권한 목록
     */
    List<MenuDto.PermissionResponse.RolePermission> selectRolePermissionsByMenuId(@Param("menuId") Long menuId);

    /**
     * 특정 역할의 모든 메뉴 권한을 조회합니다.
     *
     * @param roleId 역할 ID
     * @return 메뉴 권한 목록
     */
    List<MenuRolePermission> selectMenuPermissionsByRoleId(@Param("roleId") String roleId);

    /**
     * 특정 역할이 특정 메뉴에 접근 가능한지 확인합니다.
     *
     * @param menuId 메뉴 ID
     * @param roleId 역할 ID
     * @return 접근 가능 여부
     */
    boolean isAccessibleByRole(@Param("menuId") Long menuId, @Param("roleId") String roleId);

    /**
     * 메뉴와 역할 조합의 권한이 이미 존재하는지 확인합니다.
     *
     * @param menuId 메뉴 ID
     * @param roleId 역할 ID
     * @param excludePermissionId 제외할 권한 ID (수정 시 자기 자신 제외)
     * @return 존재 여부
     */
    boolean existsByMenuIdAndRoleId(@Param("menuId") Long menuId, @Param("roleId") String roleId, @Param("excludePermissionId") Long excludePermissionId);

    /**
     * 특정 메뉴의 모든 역할 권한을 삭제합니다.
     *
     * @param menuId 메뉴 ID
     * @param deleteUserEmail 삭제자 이메일
     * @return 삭제된 행 수
     */
    int deleteAllByMenuId(@Param("menuId") Long menuId, @Param("deleteUserEmail") String deleteUserEmail);

    /**
     * 특정 역할의 모든 메뉴 권한을 삭제합니다.
     *
     * @param roleId 역할 ID
     * @param deleteUserEmail 삭제자 이메일
     * @return 삭제된 행 수
     */
    int deleteAllByRoleId(@Param("roleId") String roleId, @Param("deleteUserEmail") String deleteUserEmail);

    /**
     * 메뉴 역할 권한을 일괄 생성합니다.
     *
     * @param menuRolePermissions 생성할 권한 목록
     * @return 생성된 행 수
     */
    int insertMenuRolePermissionBatch(@Param("list") List<MenuRolePermission> menuRolePermissions);
}
