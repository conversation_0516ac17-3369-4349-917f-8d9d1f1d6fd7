package kr.wayplus.wayplus_qr.mapper;

import kr.wayplus.wayplus_qr.dto.MenuDto;
import kr.wayplus.wayplus_qr.entity.MenuUserPermission;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Optional;

@Mapper
public interface MenuUserPermissionMapper {

    /**
     * 메뉴 사용자 권한을 생성합니다.
     *
     * @param menuUserPermission 생성할 권한 정보
     * @return 생성된 행 수
     */
    int insertMenuUserPermission(MenuUserPermission menuUserPermission);

    /**
     * 메뉴 사용자 권한을 수정합니다.
     *
     * @param menuUserPermission 수정할 권한 정보
     * @return 수정된 행 수
     */
    int updateMenuUserPermission(MenuUserPermission menuUserPermission);

    /**
     * 메뉴 사용자 권한을 논리 삭제합니다.
     *
     * @param userPermissionId 사용자 권한 ID
     * @param deleteUserEmail 삭제자 이메일
     * @return 삭제된 행 수
     */
    int deleteMenuUserPermission(@Param("userPermissionId") Long userPermissionId, @Param("deleteUserEmail") String deleteUserEmail);

    /**
     * 메뉴와 사용자로 권한 정보를 조회합니다.
     *
     * @param menuId 메뉴 ID
     * @param userEmail 사용자 이메일
     * @return 권한 정보
     */
    Optional<MenuUserPermission> selectByMenuIdAndUserEmail(@Param("menuId") Long menuId, @Param("userEmail") String userEmail);

    /**
     * 특정 메뉴의 모든 사용자 권한을 조회합니다.
     *
     * @param menuId 메뉴 ID
     * @return 사용자 권한 목록
     */
    List<MenuDto.PermissionResponse.UserPermission> selectUserPermissionsByMenuId(@Param("menuId") Long menuId);

    /**
     * 특정 사용자의 모든 메뉴 권한을 조회합니다.
     *
     * @param userEmail 사용자 이메일
     * @return 메뉴 권한 목록
     */
    List<MenuUserPermission> selectMenuPermissionsByUserEmail(@Param("userEmail") String userEmail);

    /**
     * 특정 사용자가 특정 메뉴에 접근 가능한지 확인합니다.
     *
     * @param menuId 메뉴 ID
     * @param userEmail 사용자 이메일
     * @return 접근 가능 여부
     */
    boolean isAccessibleByUser(@Param("menuId") Long menuId, @Param("userEmail") String userEmail);

    /**
     * 메뉴와 사용자 조합의 권한이 이미 존재하는지 확인합니다.
     *
     * @param menuId 메뉴 ID
     * @param userEmail 사용자 이메일
     * @param excludeUserPermissionId 제외할 사용자 권한 ID (수정 시 자기 자신 제외)
     * @return 존재 여부
     */
    boolean existsByMenuIdAndUserEmail(@Param("menuId") Long menuId, @Param("userEmail") String userEmail, @Param("excludeUserPermissionId") Long excludeUserPermissionId);

    /**
     * 특정 메뉴의 모든 사용자 권한을 삭제합니다.
     *
     * @param menuId 메뉴 ID
     * @param deleteUserEmail 삭제자 이메일
     * @return 삭제된 행 수
     */
    int deleteAllByMenuId(@Param("menuId") Long menuId, @Param("deleteUserEmail") String deleteUserEmail);

    /**
     * 특정 사용자의 모든 메뉴 권한을 삭제합니다.
     *
     * @param userEmail 사용자 이메일
     * @param deleteUserEmail 삭제자 이메일
     * @return 삭제된 행 수
     */
    int deleteAllByUserEmail(@Param("userEmail") String userEmail, @Param("deleteUserEmail") String deleteUserEmail);

    /**
     * 메뉴 사용자 권한을 일괄 생성합니다.
     *
     * @param menuUserPermissions 생성할 권한 목록
     * @return 생성된 행 수
     */
    int insertMenuUserPermissionBatch(@Param("list") List<MenuUserPermission> menuUserPermissions);
}
