package kr.wayplus.wayplus_qr.service;

import kr.wayplus.wayplus_qr.dto.request.MenuRolePermissionRequestDto;
import kr.wayplus.wayplus_qr.dto.request.MenuUserPermissionRequestDto;
import kr.wayplus.wayplus_qr.dto.response.ApiResponseDto;
import kr.wayplus.wayplus_qr.dto.response.MenuPermissionResponseDto;
import kr.wayplus.wayplus_qr.entity.MenuRolePermission;
import kr.wayplus.wayplus_qr.entity.MenuUserPermission;
import kr.wayplus.wayplus_qr.exception.ErrorCode;
import kr.wayplus.wayplus_qr.exception.QRcodeException;
import kr.wayplus.wayplus_qr.mapper.MenuMapper;
import kr.wayplus.wayplus_qr.mapper.MenuRolePermissionMapper;
import kr.wayplus.wayplus_qr.mapper.MenuUserPermissionMapper;
import kr.wayplus.wayplus_qr.mapper.UserMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class MenuPermissionService {

    private final MenuMapper menuMapper;
    private final MenuRolePermissionMapper menuRolePermissionMapper;
    private final MenuUserPermissionMapper menuUserPermissionMapper;
    private final UserMapper userMapper;

    /**
     * 메뉴 역할 권한을 설정합니다.
     *
     * @param requestDto 메뉴 역할 권한 요청 DTO
     * @param createUserEmail 생성자 이메일
     * @return 설정 결과
     */
    @Transactional
    public ApiResponseDto<Void> setMenuRolePermission(MenuRolePermissionRequestDto requestDto, String createUserEmail) {
        log.info("Setting menu role permission - Menu ID: {}, Role ID: {}, Accessible: {}", 
                requestDto.getMenuId(), requestDto.getRoleId(), requestDto.getIsAccessible());

        // 1. 메뉴 존재 확인
        menuMapper.selectMenuById(requestDto.getMenuId())
                .orElseThrow(() -> {
                    log.warn("Menu not found: {}", requestDto.getMenuId());
                    return new QRcodeException(ErrorCode.MENU_NOT_FOUND);
                });

        // 2. 기존 권한 확인
        MenuRolePermission existingPermission = menuRolePermissionMapper
                .selectByMenuIdAndRoleId(requestDto.getMenuId(), requestDto.getRoleId())
                .orElse(null);

        if (existingPermission != null) {
            // 기존 권한 수정
            MenuRolePermission updatePermission = MenuRolePermission.builder()
                    .permissionId(existingPermission.getPermissionId())
                    .isAccessible(requestDto.getIsAccessible())
                    .updateUserEmail(createUserEmail)
                    .lastUpdateDate(LocalDateTime.now())
                    .build();

            int updatedCount = menuRolePermissionMapper.updateMenuRolePermission(updatePermission);
            if (updatedCount == 0) {
                log.error("Failed to update menu role permission: {}", existingPermission.getPermissionId());
                throw new QRcodeException(ErrorCode.PERMISSION_UPDATE_FAILED);
            }
        } else {
            // 새 권한 생성
            MenuRolePermission newPermission = MenuRolePermission.builder()
                    .menuId(requestDto.getMenuId())
                    .roleId(requestDto.getRoleId())
                    .isAccessible(requestDto.getIsAccessible())
                    .createUserEmail(createUserEmail)
                    .updateUserEmail(createUserEmail)
                    .createDate(LocalDateTime.now())
                    .lastUpdateDate(LocalDateTime.now())
                    .useYn("Y")
                    .deleteYn("N")
                    .build();

            int insertedCount = menuRolePermissionMapper.insertMenuRolePermission(newPermission);
            if (insertedCount == 0) {
                log.error("Failed to create menu role permission");
                throw new QRcodeException(ErrorCode.PERMISSION_CREATION_FAILED);
            }
        }

        log.info("Menu role permission set successfully");
        return ApiResponseDto.success(null);
    }

    /**
     * 메뉴 사용자 권한을 설정합니다.
     *
     * @param requestDto 메뉴 사용자 권한 요청 DTO
     * @param createUserEmail 생성자 이메일
     * @return 설정 결과
     */
    @Transactional
    public ApiResponseDto<Void> setMenuUserPermission(MenuUserPermissionRequestDto requestDto, String createUserEmail) {
        log.info("Setting menu user permission - Menu ID: {}, User Email: {}, Accessible: {}", 
                requestDto.getMenuId(), requestDto.getUserEmail(), requestDto.getIsAccessible());

        // 1. 메뉴 존재 확인
        menuMapper.selectMenuById(requestDto.getMenuId())
                .orElseThrow(() -> {
                    log.warn("Menu not found: {}", requestDto.getMenuId());
                    return new QRcodeException(ErrorCode.MENU_NOT_FOUND);
                });

        // 2. 사용자 존재 확인
        userMapper.selectUserByEmail(requestDto.getUserEmail())
                .orElseThrow(() -> {
                    log.warn("User not found: {}", requestDto.getUserEmail());
                    return new QRcodeException(ErrorCode.USER_NOT_FOUND);
                });

        // 3. 기존 권한 확인
        MenuUserPermission existingPermission = menuUserPermissionMapper
                .selectByMenuIdAndUserEmail(requestDto.getMenuId(), requestDto.getUserEmail())
                .orElse(null);

        if (existingPermission != null) {
            // 기존 권한 수정
            MenuUserPermission updatePermission = MenuUserPermission.builder()
                    .userPermissionId(existingPermission.getUserPermissionId())
                    .isAccessible(requestDto.getIsAccessible())
                    .permissionNote(requestDto.getPermissionNote())
                    .updateUserEmail(createUserEmail)
                    .lastUpdateDate(LocalDateTime.now())
                    .build();

            int updatedCount = menuUserPermissionMapper.updateMenuUserPermission(updatePermission);
            if (updatedCount == 0) {
                log.error("Failed to update menu user permission: {}", existingPermission.getUserPermissionId());
                throw new QRcodeException(ErrorCode.PERMISSION_UPDATE_FAILED);
            }
        } else {
            // 새 권한 생성
            MenuUserPermission newPermission = requestDto.toEntity(createUserEmail);
            int insertedCount = menuUserPermissionMapper.insertMenuUserPermission(newPermission);
            if (insertedCount == 0) {
                log.error("Failed to create menu user permission");
                throw new QRcodeException(ErrorCode.PERMISSION_CREATION_FAILED);
            }
        }

        log.info("Menu user permission set successfully");
        return ApiResponseDto.success(null);
    }

    /**
     * 메뉴의 모든 권한 정보를 조회합니다.
     *
     * @param menuId 메뉴 ID
     * @return 메뉴 권한 정보
     */
    @Transactional(readOnly = true)
    public ApiResponseDto<MenuPermissionResponseDto> getMenuPermissions(Long menuId) {
        log.info("Getting menu permissions for menu ID: {}", menuId);

        // 1. 메뉴 존재 확인
        menuMapper.selectMenuById(menuId)
                .orElseThrow(() -> {
                    log.warn("Menu not found: {}", menuId);
                    return new QRcodeException(ErrorCode.MENU_NOT_FOUND);
                });

        // 2. 역할별 권한 조회
        List<MenuPermissionResponseDto.RolePermissionDto> rolePermissions = 
                menuRolePermissionMapper.selectRolePermissionsByMenuId(menuId);

        // 3. 사용자별 권한 조회
        List<MenuPermissionResponseDto.UserPermissionDto> userPermissions = 
                menuUserPermissionMapper.selectUserPermissionsByMenuId(menuId);

        // 4. 응답 DTO 구성
        MenuPermissionResponseDto responseDto = MenuPermissionResponseDto.builder()
                .menuId(menuId)
                .rolePermissions(rolePermissions)
                .userPermissions(userPermissions)
                .build();

        return ApiResponseDto.success(responseDto);
    }

    /**
     * 메뉴 역할 권한을 삭제합니다.
     *
     * @param menuId 메뉴 ID
     * @param roleId 역할 ID
     * @param deleteUserEmail 삭제자 이메일
     * @return 삭제 결과
     */
    @Transactional
    public ApiResponseDto<Void> deleteMenuRolePermission(Long menuId, String roleId, String deleteUserEmail) {
        log.info("Deleting menu role permission - Menu ID: {}, Role ID: {}", menuId, roleId);

        MenuRolePermission permission = menuRolePermissionMapper
                .selectByMenuIdAndRoleId(menuId, roleId)
                .orElseThrow(() -> {
                    log.warn("Menu role permission not found - Menu ID: {}, Role ID: {}", menuId, roleId);
                    return new QRcodeException(ErrorCode.PERMISSION_NOT_FOUND);
                });

        int deletedCount = menuRolePermissionMapper.deleteMenuRolePermission(permission.getPermissionId(), deleteUserEmail);
        if (deletedCount == 0) {
            log.error("Failed to delete menu role permission: {}", permission.getPermissionId());
            throw new QRcodeException(ErrorCode.PERMISSION_DELETE_FAILED);
        }

        log.info("Menu role permission deleted successfully");
        return ApiResponseDto.success(null);
    }

    /**
     * 메뉴 사용자 권한을 삭제합니다.
     *
     * @param menuId 메뉴 ID
     * @param userEmail 사용자 이메일
     * @param deleteUserEmail 삭제자 이메일
     * @return 삭제 결과
     */
    @Transactional
    public ApiResponseDto<Void> deleteMenuUserPermission(Long menuId, String userEmail, String deleteUserEmail) {
        log.info("Deleting menu user permission - Menu ID: {}, User Email: {}", menuId, userEmail);

        MenuUserPermission permission = menuUserPermissionMapper
                .selectByMenuIdAndUserEmail(menuId, userEmail)
                .orElseThrow(() -> {
                    log.warn("Menu user permission not found - Menu ID: {}, User Email: {}", menuId, userEmail);
                    return new QRcodeException(ErrorCode.PERMISSION_NOT_FOUND);
                });

        int deletedCount = menuUserPermissionMapper.deleteMenuUserPermission(permission.getUserPermissionId(), deleteUserEmail);
        if (deletedCount == 0) {
            log.error("Failed to delete menu user permission: {}", permission.getUserPermissionId());
            throw new QRcodeException(ErrorCode.PERMISSION_DELETE_FAILED);
        }

        log.info("Menu user permission deleted successfully");
        return ApiResponseDto.success(null);
    }

    /**
     * 사용자의 메뉴 접근 권한을 확인합니다.
     *
     * @param menuId 메뉴 ID
     * @param userEmail 사용자 이메일
     * @param roleId 사용자 역할 ID
     * @return 접근 가능 여부
     */
    @Transactional(readOnly = true)
    public boolean hasMenuAccess(Long menuId, String userEmail, String roleId) {
        // 1. 개별 사용자 권한 확인 (우선순위 높음)
        if (menuUserPermissionMapper.isAccessibleByUser(menuId, userEmail)) {
            return true;
        }

        // 2. 역할별 권한 확인
        return menuRolePermissionMapper.isAccessibleByRole(menuId, roleId);
    }
}
