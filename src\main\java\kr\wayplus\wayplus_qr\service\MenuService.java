package kr.wayplus.wayplus_qr.service;

import kr.wayplus.wayplus_qr.dto.request.MenuCreateRequestDto;
import kr.wayplus.wayplus_qr.dto.request.MenuUpdateRequestDto;
import kr.wayplus.wayplus_qr.dto.response.ApiResponseDto;
import kr.wayplus.wayplus_qr.dto.response.MenuResponseDto;
import kr.wayplus.wayplus_qr.dto.response.MenuTreeResponseDto;
import kr.wayplus.wayplus_qr.entity.Menu;
import kr.wayplus.wayplus_qr.exception.ErrorCode;
import kr.wayplus.wayplus_qr.exception.QRcodeException;
import kr.wayplus.wayplus_qr.mapper.MenuMapper;
import kr.wayplus.wayplus_qr.mapper.MenuRolePermissionMapper;
import kr.wayplus.wayplus_qr.mapper.MenuUserPermissionMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class MenuService {

    private final MenuMapper menuMapper;
    private final MenuRolePermissionMapper menuRolePermissionMapper;
    private final MenuUserPermissionMapper menuUserPermissionMapper;

    /**
     * 메뉴를 생성합니다.
     *
     * @param requestDto 메뉴 생성 요청 DTO
     * @param createUserEmail 생성자 이메일
     * @return 생성된 메뉴 ID
     */
    @Transactional
    public ApiResponseDto<Long> createMenu(MenuCreateRequestDto requestDto, String createUserEmail) {
        log.info("Creating menu with code: {} by user: {}", requestDto.getMenuCode(), createUserEmail);

        // 1. 메뉴 코드 중복 확인
        if (menuMapper.existsByMenuCode(requestDto.getMenuCode(), null)) {
            log.warn("Menu code already exists: {}", requestDto.getMenuCode());
            throw new QRcodeException(ErrorCode.DUPLICATE_MENU_CODE);
        }

        // 2. 상위 메뉴 존재 확인 (상위 메뉴가 있는 경우)
        if (requestDto.getParentMenuId() != null) {
            Menu parentMenu = menuMapper.selectMenuById(requestDto.getParentMenuId())
                    .orElseThrow(() -> {
                        log.warn("Parent menu not found: {}", requestDto.getParentMenuId());
                        return new QRcodeException(ErrorCode.MENU_NOT_FOUND);
                    });

            // 상위 메뉴가 비활성화된 경우 확인
            if (!"ACTIVE".equals(parentMenu.getStatus())) {
                log.warn("Parent menu is not active: {}", requestDto.getParentMenuId());
                throw new QRcodeException(ErrorCode.PARENT_MENU_NOT_ACTIVE);
            }
        }

        // 3. 표시 순서 자동 설정 (지정되지 않은 경우)
        if (requestDto.getDisplayOrder() == null || requestDto.getDisplayOrder() == 0) {
            Integer maxOrder = menuMapper.selectMaxDisplayOrder(requestDto.getParentMenuId(), requestDto.getMenuLevel());
            requestDto = MenuCreateRequestDto.builder()
                    .parentMenuId(requestDto.getParentMenuId())
                    .menuCode(requestDto.getMenuCode())
                    .menuName(requestDto.getMenuName())
                    .menuUrl(requestDto.getMenuUrl())
                    .menuIcon(requestDto.getMenuIcon())
                    .menuLevel(requestDto.getMenuLevel())
                    .displayOrder(maxOrder + 1)
                    .status(requestDto.getStatus())
                    .build();
        }

        // 4. 메뉴 생성
        Menu menu = requestDto.toEntity(createUserEmail);
        int insertedCount = menuMapper.insertMenu(menu);

        if (insertedCount == 0 || menu.getMenuId() == null) {
            log.error("Failed to create menu: {}", requestDto.getMenuCode());
            throw new QRcodeException(ErrorCode.MENU_CREATION_FAILED);
        }

        log.info("Menu created successfully with ID: {}", menu.getMenuId());
        return ApiResponseDto.success(menu.getMenuId());
    }

    /**
     * 메뉴를 수정합니다.
     *
     * @param menuId 메뉴 ID
     * @param requestDto 메뉴 수정 요청 DTO
     * @param updateUserEmail 수정자 이메일
     * @return 수정 결과
     */
    @Transactional
    public ApiResponseDto<Void> updateMenu(Long menuId, MenuUpdateRequestDto requestDto, String updateUserEmail) {
        log.info("Updating menu ID: {} by user: {}", menuId, updateUserEmail);

        // 1. 메뉴 존재 확인
        Menu existingMenu = menuMapper.selectMenuById(menuId)
                .orElseThrow(() -> {
                    log.warn("Menu not found: {}", menuId);
                    return new QRcodeException(ErrorCode.MENU_NOT_FOUND);
                });

        // 2. 상위 메뉴 존재 확인 (상위 메뉴가 변경된 경우)
        if (requestDto.getParentMenuId() != null && !requestDto.getParentMenuId().equals(existingMenu.getParentMenuId())) {
            Menu parentMenu = menuMapper.selectMenuById(requestDto.getParentMenuId())
                    .orElseThrow(() -> {
                        log.warn("Parent menu not found: {}", requestDto.getParentMenuId());
                        return new QRcodeException(ErrorCode.MENU_NOT_FOUND);
                    });

            // 자기 자신을 상위 메뉴로 설정하는 것 방지
            if (requestDto.getParentMenuId().equals(menuId)) {
                log.warn("Cannot set self as parent menu: {}", menuId);
                throw new QRcodeException(ErrorCode.INVALID_PARENT_MENU);
            }

            // 순환 참조 방지 (하위 메뉴를 상위 메뉴로 설정하는 것 방지)
            if (isDescendantMenu(menuId, requestDto.getParentMenuId())) {
                log.warn("Cannot set descendant as parent menu: {} -> {}", menuId, requestDto.getParentMenuId());
                throw new QRcodeException(ErrorCode.CIRCULAR_REFERENCE);
            }
        }

        // 3. 메뉴 정보 업데이트
        Menu updateMenu = Menu.builder()
                .menuId(menuId)
                .parentMenuId(requestDto.getParentMenuId())
                .menuName(requestDto.getMenuName())
                .menuUrl(requestDto.getMenuUrl())
                .menuIcon(requestDto.getMenuIcon())
                .menuLevel(requestDto.getMenuLevel())
                .displayOrder(requestDto.getDisplayOrder())
                .status(requestDto.getStatus())
                .updateUserEmail(updateUserEmail)
                .lastUpdateDate(LocalDateTime.now())
                .build();

        int updatedCount = menuMapper.updateMenu(updateMenu);
        if (updatedCount == 0) {
            log.error("Failed to update menu: {}", menuId);
            throw new QRcodeException(ErrorCode.MENU_UPDATE_FAILED);
        }

        log.info("Menu updated successfully: {}", menuId);
        return ApiResponseDto.success(null);
    }

    /**
     * 메뉴를 삭제합니다.
     *
     * @param menuId 메뉴 ID
     * @param deleteUserEmail 삭제자 이메일
     * @return 삭제 결과
     */
    @Transactional
    public ApiResponseDto<Void> deleteMenu(Long menuId, String deleteUserEmail) {
        log.info("Deleting menu ID: {} by user: {}", menuId, deleteUserEmail);

        // 1. 메뉴 존재 확인
        Menu menu = menuMapper.selectMenuById(menuId)
                .orElseThrow(() -> {
                    log.warn("Menu not found: {}", menuId);
                    return new QRcodeException(ErrorCode.MENU_NOT_FOUND);
                });

        // 2. 하위 메뉴 존재 확인
        if (menuMapper.hasChildMenus(menuId)) {
            log.warn("Cannot delete menu with child menus: {}", menuId);
            throw new QRcodeException(ErrorCode.MENU_HAS_CHILDREN);
        }

        // 3. 메뉴 삭제
        int deletedCount = menuMapper.deleteMenu(menuId, deleteUserEmail);
        if (deletedCount == 0) {
            log.error("Failed to delete menu: {}", menuId);
            throw new QRcodeException(ErrorCode.MENU_DELETE_FAILED);
        }

        // 4. 관련 권한 정보도 삭제
        menuRolePermissionMapper.deleteAllByMenuId(menuId, deleteUserEmail);
        menuUserPermissionMapper.deleteAllByMenuId(menuId, deleteUserEmail);

        log.info("Menu deleted successfully: {}", menuId);
        return ApiResponseDto.success(null);
    }

    /**
     * 메뉴 ID로 메뉴 정보를 조회합니다.
     *
     * @param menuId 메뉴 ID
     * @return 메뉴 정보
     */
    @Transactional(readOnly = true)
    public ApiResponseDto<MenuResponseDto> getMenuById(Long menuId) {
        log.info("Getting menu by ID: {}", menuId);

        Menu menu = menuMapper.selectMenuById(menuId)
                .orElseThrow(() -> {
                    log.warn("Menu not found: {}", menuId);
                    return new QRcodeException(ErrorCode.MENU_NOT_FOUND);
                });

        MenuResponseDto responseDto = convertToMenuResponseDto(menu);
        return ApiResponseDto.success(responseDto);
    }

    /**
     * 모든 활성 메뉴 목록을 조회합니다.
     *
     * @return 메뉴 목록
     */
    @Transactional(readOnly = true)
    public ApiResponseDto<List<MenuResponseDto>> getAllActiveMenus() {
        log.info("Getting all active menus");

        List<MenuResponseDto> menus = menuMapper.selectAllActiveMenus();
        return ApiResponseDto.success(menus);
    }

    /**
     * 계층형 메뉴 트리를 조회합니다.
     *
     * @return 메뉴 트리
     */
    @Transactional(readOnly = true)
    public ApiResponseDto<List<MenuTreeResponseDto>> getMenuTree() {
        log.info("Getting menu tree");

        List<MenuTreeResponseDto> allMenus = menuMapper.selectMenuTree();
        List<MenuTreeResponseDto> menuTree = buildMenuTree(allMenus);
        return ApiResponseDto.success(menuTree);
    }

    /**
     * 특정 사용자가 접근 가능한 메뉴 트리를 조회합니다.
     *
     * @param userEmail 사용자 이메일
     * @param roleId 사용자 역할 ID
     * @return 접근 가능한 메뉴 트리
     */
    @Transactional(readOnly = true)
    public ApiResponseDto<List<MenuTreeResponseDto>> getAccessibleMenuTree(String userEmail, String roleId) {
        log.info("Getting accessible menu tree for user: {} with role: {}", userEmail, roleId);

        List<MenuTreeResponseDto> accessibleMenus = menuMapper.selectAccessibleMenuTree(userEmail, roleId);
        List<MenuTreeResponseDto> menuTree = buildMenuTree(accessibleMenus);
        return ApiResponseDto.success(menuTree);
    }

    /**
     * 하위 메뉴인지 확인합니다 (순환 참조 방지용).
     *
     * @param parentMenuId 상위 메뉴 ID
     * @param childMenuId 하위 메뉴 ID
     * @return 하위 메뉴 여부
     */
    private boolean isDescendantMenu(Long parentMenuId, Long childMenuId) {
        List<MenuResponseDto> childMenus = menuMapper.selectMenusByParentId(parentMenuId);
        for (MenuResponseDto childMenu : childMenus) {
            if (childMenu.getMenuId().equals(childMenuId)) {
                return true;
            }
            if (isDescendantMenu(childMenu.getMenuId(), childMenuId)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Menu 엔티티를 MenuResponseDto로 변환합니다.
     *
     * @param menu Menu 엔티티
     * @return MenuResponseDto
     */
    private MenuResponseDto convertToMenuResponseDto(Menu menu) {
        return MenuResponseDto.builder()
                .menuId(menu.getMenuId())
                .parentMenuId(menu.getParentMenuId())
                .menuCode(menu.getMenuCode())
                .menuName(menu.getMenuName())
                .menuUrl(menu.getMenuUrl())
                .menuIcon(menu.getMenuIcon())
                .menuLevel(menu.getMenuLevel())
                .displayOrder(menu.getDisplayOrder())
                .status(menu.getStatus())
                .createUserEmail(menu.getCreateUserEmail())
                .createDate(menu.getCreateDate())
                .updateUserEmail(menu.getUpdateUserEmail())
                .lastUpdateDate(menu.getLastUpdateDate())
                .build();
    }

    /**
     * 평면 메뉴 목록을 계층형 트리 구조로 변환합니다.
     *
     * @param allMenus 평면 메뉴 목록
     * @return 계층형 메뉴 트리
     */
    private List<MenuTreeResponseDto> buildMenuTree(List<MenuTreeResponseDto> allMenus) {
        Map<Long, MenuTreeResponseDto> menuMap = allMenus.stream()
                .collect(Collectors.toMap(MenuTreeResponseDto::getMenuId, menu -> {
                    // children 리스트 초기화
                    return MenuTreeResponseDto.builder()
                            .menuId(menu.getMenuId())
                            .parentMenuId(menu.getParentMenuId())
                            .menuCode(menu.getMenuCode())
                            .menuName(menu.getMenuName())
                            .menuUrl(menu.getMenuUrl())
                            .menuIcon(menu.getMenuIcon())
                            .menuLevel(menu.getMenuLevel())
                            .displayOrder(menu.getDisplayOrder())
                            .status(menu.getStatus())
                            .accessible(menu.getAccessible())
                            .children(new ArrayList<>())
                            .build();
                }));

        List<MenuTreeResponseDto> rootMenus = new ArrayList<>();

        for (MenuTreeResponseDto menu : allMenus) {
            MenuTreeResponseDto menuWithChildren = menuMap.get(menu.getMenuId());
            if (menu.getParentMenuId() == null) {
                // 최상위 메뉴
                rootMenus.add(menuWithChildren);
            } else {
                // 하위 메뉴
                MenuTreeResponseDto parent = menuMap.get(menu.getParentMenuId());
                if (parent != null) {
                    parent.getChildren().add(menuWithChildren);
                }
            }
        }

        return rootMenus;
    }
}
