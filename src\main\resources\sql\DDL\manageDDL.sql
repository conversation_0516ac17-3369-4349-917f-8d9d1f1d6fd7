-- 관리자 메뉴 관리 시스템 DDL
-- 기존 settingDDL.sql의 공통 컬럼 패턴을 따름

-- 관리자 메뉴 정의 테이블
CREATE TABLE manage_menus (
    menu_id         BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '메뉴 고유 번호 (PK)',
    parent_menu_id  BIGINT UNSIGNED NULL COMMENT '상위 메뉴 ID (FK, 최상위 메뉴인 경우 NULL)',
    menu_code       VARCHAR(100) NOT NULL UNIQUE COMMENT '메뉴 코드 (영문/숫자, 예: DASHBOARD, QR_MANAGEMENT)',
    menu_name       VARCHAR(255) NOT NULL COMMENT '메뉴 이름 (화면에 표시될 이름)',
    menu_name_en    VARCHAR(255) COMMENT '메뉴 영문 이름 (다국어 지원용)',
    description     TEXT COMMENT '메뉴 설명',
    menu_url        VARCHAR(500) COMMENT '메뉴 URL 경로 (프론트엔드 라우팅용)',
    menu_icon       VARCHAR(100) COMMENT '메뉴 아이콘 클래스명 또는 경로',
    menu_level      INT UNSIGNED NOT NULL DEFAULT 1 COMMENT '메뉴 레벨 (1: 최상위, 2: 2단계, 3: 3단계)',
    display_order   INT UNSIGNED DEFAULT 0 COMMENT '메뉴 표시 순서 (같은 레벨 내에서)',
    is_visible      ENUM('Y', 'N') NOT NULL DEFAULT 'Y' COMMENT '메뉴 표시 여부',
    is_external     ENUM('Y', 'N') NOT NULL DEFAULT 'N' COMMENT '외부 링크 여부',
    target_type     ENUM('_self', '_blank', '_parent', '_top') DEFAULT '_self' COMMENT '링크 타겟 타입',
    status          ENUM('ACTIVE', 'INACTIVE') NOT NULL DEFAULT 'ACTIVE' COMMENT '메뉴 상태',
    create_user_email VARCHAR(255) COMMENT '생성자 user_email (FK)',
    create_date     DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '생성 시기',
    update_user_email VARCHAR(255) COMMENT '수정자 user_email (FK)',
    last_update_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '수정 시기',
    delete_user_email VARCHAR(255) COMMENT '삭제자 user_email (FK)',
    delete_date     DATETIME COMMENT '삭제 일시',
    use_yn          ENUM('Y', 'N') NOT NULL DEFAULT 'Y' COMMENT '사용 여부',
    delete_yn       ENUM('Y', 'N') NOT NULL DEFAULT 'N' COMMENT '삭제 여부',
    FOREIGN KEY (parent_menu_id) REFERENCES manage_menus(menu_id) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (create_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (update_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (delete_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE
) COMMENT '관리자 시스템 메뉴 정의';

CREATE INDEX idx_manage_menus_parent_id ON manage_menus(parent_menu_id);
CREATE INDEX idx_manage_menus_menu_code ON manage_menus(menu_code);
CREATE INDEX idx_manage_menus_menu_level ON manage_menus(menu_level);
CREATE INDEX idx_manage_menus_display_order ON manage_menus(display_order);
CREATE INDEX idx_manage_menus_status ON manage_menus(status);
CREATE INDEX idx_manage_menus_delete_yn ON manage_menus(delete_yn);

-- 역할별 메뉴 권한 매핑 테이블
CREATE TABLE manage_menu_role_permissions (
    permission_id   BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '권한 매핑 고유 번호 (PK)',
    menu_id         BIGINT UNSIGNED NOT NULL COMMENT '메뉴 고유 번호 (FK)',
    role_id         VARCHAR(50) NOT NULL COMMENT '역할 ID (FK, roles.role_id 참조)',
    permission_type ENUM('READ', 'WRITE', 'DELETE', 'ADMIN') NOT NULL DEFAULT 'READ' COMMENT '권한 타입',
    is_accessible   ENUM('Y', 'N') NOT NULL DEFAULT 'Y' COMMENT '접근 가능 여부',
    create_user_email VARCHAR(255) COMMENT '생성자 user_email (FK)',
    create_date     DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '생성 시기',
    update_user_email VARCHAR(255) COMMENT '수정자 user_email (FK)',
    last_update_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '수정 시기',
    delete_user_email VARCHAR(255) COMMENT '삭제자 user_email (FK)',
    delete_date     DATETIME COMMENT '삭제 일시',
    use_yn          ENUM('Y', 'N') NOT NULL DEFAULT 'Y' COMMENT '사용 여부',
    delete_yn       ENUM('Y', 'N') NOT NULL DEFAULT 'N' COMMENT '삭제 여부',
    UNIQUE KEY uk_menu_role_permission (menu_id, role_id, permission_type, delete_yn),
    FOREIGN KEY (menu_id) REFERENCES manage_menus(menu_id) ON DELETE CASCADE ON UPDATE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(role_id) ON DELETE CASCADE ON UPDATE CASCADE,
    FOREIGN KEY (create_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (update_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (delete_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE
) COMMENT '역할별 메뉴 접근 권한 매핑';

CREATE INDEX idx_manage_menu_role_permissions_menu_id ON manage_menu_role_permissions(menu_id);
CREATE INDEX idx_manage_menu_role_permissions_role_id ON manage_menu_role_permissions(role_id);
CREATE INDEX idx_manage_menu_role_permissions_permission_type ON manage_menu_role_permissions(permission_type);
CREATE INDEX idx_manage_menu_role_permissions_delete_yn ON manage_menu_role_permissions(delete_yn);

-- 사용자별 개인화 메뉴 설정 테이블 (선택적)
CREATE TABLE manage_user_menu_preferences (
    preference_id   BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '개인화 설정 고유 번호 (PK)',
    user_email      VARCHAR(255) NOT NULL COMMENT '사용자 이메일 (FK)',
    menu_id         BIGINT UNSIGNED NOT NULL COMMENT '메뉴 고유 번호 (FK)',
    is_favorite     ENUM('Y', 'N') NOT NULL DEFAULT 'N' COMMENT '즐겨찾기 여부',
    is_hidden       ENUM('Y', 'N') NOT NULL DEFAULT 'N' COMMENT '메뉴 숨김 여부',
    custom_order    INT UNSIGNED COMMENT '사용자 정의 순서',
    last_accessed_date DATETIME COMMENT '마지막 접근 일시',
    access_count    BIGINT UNSIGNED DEFAULT 0 COMMENT '접근 횟수',
    create_date     DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '생성 시기',
    last_update_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '수정 시기',
    UNIQUE KEY uk_user_menu_preference (user_email, menu_id),
    FOREIGN KEY (user_email) REFERENCES users(user_email) ON DELETE CASCADE ON UPDATE CASCADE,
    FOREIGN KEY (menu_id) REFERENCES manage_menus(menu_id) ON DELETE CASCADE ON UPDATE CASCADE
) COMMENT '사용자별 메뉴 개인화 설정';

CREATE INDEX idx_manage_user_menu_preferences_user_email ON manage_user_menu_preferences(user_email);
CREATE INDEX idx_manage_user_menu_preferences_menu_id ON manage_user_menu_preferences(menu_id);
CREATE INDEX idx_manage_user_menu_preferences_is_favorite ON manage_user_menu_preferences(is_favorite);

-- 메뉴 접근 로그 테이블 (통계 및 감사용)
CREATE TABLE manage_menu_access_logs (
    log_id          BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '로그 고유 번호 (PK)',
    user_email      VARCHAR(255) NOT NULL COMMENT '접근한 사용자 이메일 (FK)',
    menu_id         BIGINT UNSIGNED NOT NULL COMMENT '접근한 메뉴 고유 번호 (FK)',
    access_time     DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '접근 시각',
    ip_address      VARCHAR(45) COMMENT '접근 IP 주소',
    user_agent      TEXT COMMENT 'User Agent 정보',
    session_id      VARCHAR(100) COMMENT '세션 ID',
    access_type     ENUM('VIEW', 'ACTION') NOT NULL DEFAULT 'VIEW' COMMENT '접근 타입 (조회/액션)',
    response_time   INT UNSIGNED COMMENT '응답 시간 (ms)',
    FOREIGN KEY (user_email) REFERENCES users(user_email) ON DELETE CASCADE ON UPDATE CASCADE,
    FOREIGN KEY (menu_id) REFERENCES manage_menus(menu_id) ON DELETE CASCADE ON UPDATE CASCADE
) COMMENT '메뉴 접근 로그 (통계 및 감사용)';

CREATE INDEX idx_manage_menu_access_logs_user_email ON manage_menu_access_logs(user_email);
CREATE INDEX idx_manage_menu_access_logs_menu_id ON manage_menu_access_logs(menu_id);
CREATE INDEX idx_manage_menu_access_logs_access_time ON manage_menu_access_logs(access_time);
CREATE INDEX idx_manage_menu_access_logs_access_type ON manage_menu_access_logs(access_type);

-- 기본 메뉴 데이터 삽입
INSERT INTO manage_menus (menu_code, menu_name, menu_name_en, description, menu_url, menu_icon, menu_level, display_order, create_user_email) VALUES
-- 1단계 메뉴 (최상위)
('DASHBOARD', '대시보드', 'Dashboard', '시스템 전체 현황 및 통계', '/admin/dashboard', 'fas fa-tachometer-alt', 1, 1, '<EMAIL>'),
('QR_MANAGEMENT', 'QR 관리', 'QR Management', 'QR 코드 생성 및 관리', '/admin/qr', 'fas fa-qrcode', 1, 2, '<EMAIL>'),
('PROJECT_MANAGEMENT', '프로젝트 관리', 'Project Management', '프로젝트 및 업체 관리', '/admin/projects', 'fas fa-building', 1, 3, '<EMAIL>'),
('EVENT_MANAGEMENT', '이벤트 관리', 'Event Management', '이벤트 및 참가자 관리', '/admin/events', 'fas fa-calendar-alt', 1, 4, '<EMAIL>'),
('USER_MANAGEMENT', '사용자 관리', 'User Management', '관리자 및 사용자 관리', '/admin/users', 'fas fa-users', 1, 5, '<EMAIL>'),
('STATISTICS', '통계 분석', 'Statistics', '각종 통계 및 분석 리포트', '/admin/statistics', 'fas fa-chart-bar', 1, 6, '<EMAIL>'),
('SYSTEM_SETTINGS', '시스템 설정', 'System Settings', '시스템 환경 설정', '/admin/settings', 'fas fa-cogs', 1, 7, '<EMAIL>');
