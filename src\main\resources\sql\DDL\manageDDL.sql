-- SUPER_ADMIN 전용 메뉴 관리 시스템 DDL
-- 기존 settingDDL.sql의 공통 컬럼 패턴을 따름

-- 관리자 메뉴 정의 테이블
CREATE TABLE manage_menus (
    menu_id         BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '메뉴 고유 번호 (PK)',
    parent_menu_id  BIGINT UNSIGNED NULL COMMENT '상위 메뉴 ID (FK, 최상위 메뉴인 경우 NULL)',
    menu_code       VARCHAR(100) NOT NULL UNIQUE COMMENT '메뉴 코드 (영문/숫자, 예: DASHBOARD, QR_MANAGEMENT)',
    menu_name       VARCHAR(255) NOT NULL COMMENT '메뉴 이름 (화면에 표시될 이름)',
    menu_url        VARCHAR(500) COMMENT '메뉴 URL 경로 (프론트엔드 라우팅용)',
    menu_icon       VARCHAR(100) COMMENT '메뉴 아이콘 클래스명 또는 경로',
    menu_level      INT UNSIGNED NOT NULL DEFAULT 1 COMMENT '메뉴 레벨 (1: 최상위, 2: 2단계, 3: 3단계)',
    display_order   INT UNSIGNED DEFAULT 0 COMMENT '메뉴 표시 순서 (같은 레벨 내에서)',
    is_visible      ENUM('Y', 'N') NOT NULL DEFAULT 'Y' COMMENT '메뉴 표시 여부',
    status          ENUM('ACTIVE', 'INACTIVE') NOT NULL DEFAULT 'ACTIVE' COMMENT '메뉴 상태',
    create_user_email VARCHAR(255) COMMENT '생성자 user_email (FK)',
    create_date     DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '생성 시기',
    update_user_email VARCHAR(255) COMMENT '수정자 user_email (FK)',
    last_update_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '수정 시기',
    delete_user_email VARCHAR(255) COMMENT '삭제자 user_email (FK)',
    delete_date     DATETIME COMMENT '삭제 일시',
    use_yn          ENUM('Y', 'N') NOT NULL DEFAULT 'Y' COMMENT '사용 여부',
    delete_yn       ENUM('Y', 'N') NOT NULL DEFAULT 'N' COMMENT '삭제 여부',
    FOREIGN KEY (parent_menu_id) REFERENCES manage_menus(menu_id) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (create_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (update_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (delete_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE
) COMMENT '관리자 시스템 메뉴 정의 (SUPER_ADMIN 관리)';

CREATE INDEX idx_manage_menus_parent_id ON manage_menus(parent_menu_id);
CREATE INDEX idx_manage_menus_menu_code ON manage_menus(menu_code);
CREATE INDEX idx_manage_menus_menu_level ON manage_menus(menu_level);
CREATE INDEX idx_manage_menus_display_order ON manage_menus(display_order);
CREATE INDEX idx_manage_menus_status ON manage_menus(status);
CREATE INDEX idx_manage_menus_delete_yn ON manage_menus(delete_yn);

-- 메뉴별 역할 접근 권한 테이블 (단순화된 버전)
CREATE TABLE manage_menu_role_permissions (
    permission_id   BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '권한 매핑 고유 번호 (PK)',
    menu_id         BIGINT UNSIGNED NOT NULL COMMENT '메뉴 고유 번호 (FK)',
    role_id         VARCHAR(50) NOT NULL COMMENT '역할 ID (FK, roles.role_id 참조)',
    is_accessible   ENUM('Y', 'N') NOT NULL DEFAULT 'Y' COMMENT '접근 가능 여부',
    create_user_email VARCHAR(255) COMMENT '생성자 user_email (FK)',
    create_date     DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '생성 시기',
    update_user_email VARCHAR(255) COMMENT '수정자 user_email (FK)',
    last_update_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '수정 시기',
    delete_user_email VARCHAR(255) COMMENT '삭제자 user_email (FK)',
    delete_date     DATETIME COMMENT '삭제 일시',
    use_yn          ENUM('Y', 'N') NOT NULL DEFAULT 'Y' COMMENT '사용 여부',
    delete_yn       ENUM('Y', 'N') NOT NULL DEFAULT 'N' COMMENT '삭제 여부',
    UNIQUE KEY uk_menu_role_permission (menu_id, role_id, delete_yn),
    FOREIGN KEY (menu_id) REFERENCES manage_menus(menu_id) ON DELETE CASCADE ON UPDATE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(role_id) ON DELETE CASCADE ON UPDATE CASCADE,
    FOREIGN KEY (create_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (update_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (delete_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE
) COMMENT '메뉴별 역할 접근 권한 매핑 (SUPER_ADMIN 관리)';

CREATE INDEX idx_manage_menu_role_permissions_menu_id ON manage_menu_role_permissions(menu_id);
CREATE INDEX idx_manage_menu_role_permissions_role_id ON manage_menu_role_permissions(role_id);
CREATE INDEX idx_manage_menu_role_permissions_delete_yn ON manage_menu_role_permissions(delete_yn);

-- 기본 메뉴 데이터 삽입
INSERT INTO manage_menus (menu_code, menu_name, menu_url, menu_icon, menu_level, display_order, create_user_email) VALUES
-- 1단계 메뉴 (최상위)
('DASHBOARD', '대시보드', '/admin/dashboard', 'fas fa-tachometer-alt', 1, 1, '<EMAIL>'),
('QR_MANAGEMENT', 'QR 관리', '/admin/qr', 'fas fa-qrcode', 1, 2, '<EMAIL>'),
('PROJECT_MANAGEMENT', '프로젝트 관리', '/admin/projects', 'fas fa-building', 1, 3, '<EMAIL>'),
('EVENT_MANAGEMENT', '이벤트 관리', '/admin/events', 'fas fa-calendar-alt', 1, 4, '<EMAIL>'),
('USER_MANAGEMENT', '사용자 관리', '/admin/users', 'fas fa-users', 1, 5, '<EMAIL>'),
('STATISTICS', '통계 분석', '/admin/statistics', 'fas fa-chart-bar', 1, 6, '<EMAIL>'),
('SYSTEM_SETTINGS', '시스템 설정', '/admin/settings', 'fas fa-cogs', 1, 7, '<EMAIL>');

-- 2단계 메뉴 (하위 메뉴) 추가
INSERT INTO manage_menus (parent_menu_id, menu_code, menu_name, menu_url, menu_icon, menu_level, display_order, create_user_email) VALUES
-- QR 관리 하위 메뉴
((SELECT menu_id FROM manage_menus WHERE menu_code = 'QR_MANAGEMENT'), 'QR_LIST', 'QR 목록', '/admin/qr/list', 'fas fa-list', 2, 1, '<EMAIL>'),
((SELECT menu_id FROM manage_menus WHERE menu_code = 'QR_MANAGEMENT'), 'QR_CREATE', 'QR 생성', '/admin/qr/create', 'fas fa-plus', 2, 2, '<EMAIL>'),
((SELECT menu_id FROM manage_menus WHERE menu_code = 'QR_MANAGEMENT'), 'QR_STATISTICS', 'QR 통계', '/admin/qr/statistics', 'fas fa-chart-line', 2, 3, '<EMAIL>'),

-- 프로젝트 관리 하위 메뉴
((SELECT menu_id FROM manage_menus WHERE menu_code = 'PROJECT_MANAGEMENT'), 'PROJECT_LIST', '프로젝트 목록', '/admin/projects/list', 'fas fa-list', 2, 1, '<EMAIL>'),
((SELECT menu_id FROM manage_menus WHERE menu_code = 'PROJECT_MANAGEMENT'), 'PROJECT_CREATE', '프로젝트 생성', '/admin/projects/create', 'fas fa-plus', 2, 2, '<EMAIL>'),

-- 이벤트 관리 하위 메뉴
((SELECT menu_id FROM manage_menus WHERE menu_code = 'EVENT_MANAGEMENT'), 'EVENT_LIST', '이벤트 목록', '/admin/events/list', 'fas fa-list', 2, 1, '<EMAIL>'),
((SELECT menu_id FROM manage_menus WHERE menu_code = 'EVENT_MANAGEMENT'), 'EVENT_CREATE', '이벤트 생성', '/admin/events/create', 'fas fa-plus', 2, 2, '<EMAIL>'),
((SELECT menu_id FROM manage_menus WHERE menu_code = 'EVENT_MANAGEMENT'), 'ATTENDEE_MANAGEMENT', '참가자 관리', '/admin/events/attendees', 'fas fa-users', 2, 3, '<EMAIL>'),

-- 사용자 관리 하위 메뉴
((SELECT menu_id FROM manage_menus WHERE menu_code = 'USER_MANAGEMENT'), 'USER_LIST', '사용자 목록', '/admin/users/list', 'fas fa-list', 2, 1, '<EMAIL>'),
((SELECT menu_id FROM manage_menus WHERE menu_code = 'USER_MANAGEMENT'), 'USER_CREATE', '사용자 생성', '/admin/users/create', 'fas fa-user-plus', 2, 2, '<EMAIL>'),
((SELECT menu_id FROM manage_menus WHERE menu_code = 'USER_MANAGEMENT'), 'USER_ROLES', '역할 관리', '/admin/users/roles', 'fas fa-user-tag', 2, 3, '<EMAIL>'),

-- 통계 분석 하위 메뉴
((SELECT menu_id FROM manage_menus WHERE menu_code = 'STATISTICS'), 'QR_STATS', 'QR 통계', '/admin/statistics/qr', 'fas fa-qrcode', 2, 1, '<EMAIL>'),
((SELECT menu_id FROM manage_menus WHERE menu_code = 'STATISTICS'), 'EVENT_STATS', '이벤트 통계', '/admin/statistics/events', 'fas fa-calendar', 2, 2, '<EMAIL>'),
((SELECT menu_id FROM manage_menus WHERE menu_code = 'STATISTICS'), 'USER_STATS', '사용자 통계', '/admin/statistics/users', 'fas fa-users', 2, 3, '<EMAIL>'),

-- 시스템 설정 하위 메뉴
((SELECT menu_id FROM manage_menus WHERE menu_code = 'SYSTEM_SETTINGS'), 'MENU_MANAGEMENT', '메뉴 관리', '/admin/settings/menus', 'fas fa-sitemap', 2, 1, '<EMAIL>'),
((SELECT menu_id FROM manage_menus WHERE menu_code = 'SYSTEM_SETTINGS'), 'MAIL_SETTINGS', '메일 설정', '/admin/settings/mail', 'fas fa-envelope', 2, 2, '<EMAIL>'),
((SELECT menu_id FROM manage_menus WHERE menu_code = 'SYSTEM_SETTINGS'), 'SYSTEM_CONFIG', '시스템 설정', '/admin/settings/config', 'fas fa-cog', 2, 3, '<EMAIL>');

-- 기본 역할별 메뉴 접근 권한 설정
-- SUPER_ADMIN은 모든 메뉴에 접근 가능
INSERT INTO manage_menu_role_permissions (menu_id, role_id, create_user_email)
SELECT menu_id, 'SUPER_ADMIN', '<EMAIL>'
FROM manage_menus
WHERE delete_yn = 'N';

-- PROJECT_ADMIN 기본 접근 권한 (시스템 설정 제외)
INSERT INTO manage_menu_role_permissions (menu_id, role_id, create_user_email)
SELECT menu_id, 'PROJECT_ADMIN', '<EMAIL>'
FROM manage_menus
WHERE menu_code NOT IN ('SYSTEM_SETTINGS', 'MENU_MANAGEMENT', 'SYSTEM_CONFIG')
AND delete_yn = 'N';

-- SUB_ADMIN 기본 접근 권한 (조회 위주)
INSERT INTO manage_menu_role_permissions (menu_id, role_id, create_user_email)
SELECT menu_id, 'SUB_ADMIN', '<EMAIL>'
FROM manage_menus
WHERE menu_code IN ('DASHBOARD', 'QR_LIST', 'QR_STATISTICS', 'PROJECT_LIST', 'EVENT_LIST', 'ATTENDEE_MANAGEMENT', 'USER_LIST', 'QR_STATS', 'EVENT_STATS', 'USER_STATS')
AND delete_yn = 'N';

-- VIEWER 기본 접근 권한 (조회만)
INSERT INTO manage_menu_role_permissions (menu_id, role_id, create_user_email)
SELECT menu_id, 'VIEWER', '<EMAIL>'
FROM manage_menus
WHERE menu_code IN ('DASHBOARD', 'QR_LIST', 'QR_STATISTICS', 'PROJECT_LIST', 'EVENT_LIST', 'QR_STATS', 'EVENT_STATS')
AND delete_yn = 'N';
