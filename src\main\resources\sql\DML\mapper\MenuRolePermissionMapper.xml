<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.wayplus.wayplus_qr.mapper.MenuRolePermissionMapper">

    <!-- MenuRolePermission 엔티티 ResultMap -->
    <resultMap id="menuRolePermissionResultMap" type="kr.wayplus.wayplus_qr.entity.MenuRolePermission">
        <id property="permissionId" column="permission_id"/>
        <result property="menuId" column="menu_id"/>
        <result property="roleId" column="role_id"/>
        <result property="isAccessible" column="is_accessible"/>
        <result property="createUserEmail" column="create_user_email"/>
        <result property="createDate" column="create_date"/>
        <result property="updateUserEmail" column="update_user_email"/>
        <result property="lastUpdateDate" column="last_update_date"/>
        <result property="deleteUserEmail" column="delete_user_email"/>
        <result property="deleteDate" column="delete_date"/>
        <result property="useYn" column="use_yn"/>
        <result property="deleteYn" column="delete_yn"/>
    </resultMap>

    <!-- RolePermissionDto ResultMap -->
    <resultMap id="rolePermissionDtoResultMap" type="kr.wayplus.wayplus_qr.dto.response.MenuPermissionResponseDto$RolePermissionDto">
        <id property="permissionId" column="permission_id"/>
        <result property="roleId" column="role_id"/>
        <result property="roleName" column="role_name"/>
        <result property="isAccessible" column="is_accessible"/>
        <result property="createDate" column="create_date"/>
        <result property="createUserEmail" column="create_user_email"/>
    </resultMap>

    <!-- 메뉴 역할 권한 생성 -->
    <insert id="insertMenuRolePermission" parameterType="kr.wayplus.wayplus_qr.entity.MenuRolePermission" useGeneratedKeys="true" keyProperty="permissionId">
        INSERT INTO manage_menu_role_permissions (
            menu_id, role_id, is_accessible,
            create_user_email, create_date, update_user_email, last_update_date,
            use_yn, delete_yn
        ) VALUES (
            #{menuId}, #{roleId}, #{isAccessible},
            #{createUserEmail}, #{createDate}, #{updateUserEmail}, #{lastUpdateDate},
            #{useYn}, #{deleteYn}
        )
    </insert>

    <!-- 메뉴 역할 권한 수정 -->
    <update id="updateMenuRolePermission" parameterType="kr.wayplus.wayplus_qr.entity.MenuRolePermission">
        UPDATE manage_menu_role_permissions
        SET is_accessible = #{isAccessible},
            update_user_email = #{updateUserEmail},
            last_update_date = #{lastUpdateDate}
        WHERE permission_id = #{permissionId}
          AND delete_yn = 'N'
    </update>

    <!-- 메뉴 역할 권한 논리 삭제 -->
    <update id="deleteMenuRolePermission">
        UPDATE manage_menu_role_permissions
        SET delete_yn = 'Y',
            delete_user_email = #{deleteUserEmail},
            delete_date = NOW()
        WHERE permission_id = #{permissionId}
          AND delete_yn = 'N'
    </update>

    <!-- 메뉴와 역할로 권한 조회 -->
    <select id="selectByMenuIdAndRoleId" resultMap="menuRolePermissionResultMap">
        SELECT *
        FROM manage_menu_role_permissions
        WHERE menu_id = #{menuId}
          AND role_id = #{roleId}
          AND delete_yn = 'N'
    </select>

    <!-- 특정 메뉴의 모든 역할 권한 조회 -->
    <select id="selectRolePermissionsByMenuId" resultMap="rolePermissionDtoResultMap">
        SELECT mrp.permission_id, mrp.role_id, r.role_name, mrp.is_accessible,
               mrp.create_date, mrp.create_user_email
        FROM manage_menu_role_permissions mrp
        LEFT JOIN roles r ON mrp.role_id = r.role_id
        WHERE mrp.menu_id = #{menuId}
          AND mrp.delete_yn = 'N'
          AND mrp.use_yn = 'Y'
        ORDER BY mrp.role_id
    </select>

    <!-- 특정 역할의 모든 메뉴 권한 조회 -->
    <select id="selectMenuPermissionsByRoleId" resultMap="menuRolePermissionResultMap">
        SELECT *
        FROM manage_menu_role_permissions
        WHERE role_id = #{roleId}
          AND delete_yn = 'N'
          AND use_yn = 'Y'
        ORDER BY menu_id
    </select>

    <!-- 역할별 메뉴 접근 가능 여부 확인 -->
    <select id="isAccessibleByRole" resultType="boolean">
        SELECT COUNT(*) > 0
        FROM manage_menu_role_permissions
        WHERE menu_id = #{menuId}
          AND role_id = #{roleId}
          AND is_accessible = 'Y'
          AND delete_yn = 'N'
          AND use_yn = 'Y'
    </select>

    <!-- 메뉴와 역할 조합 존재 여부 확인 -->
    <select id="existsByMenuIdAndRoleId" resultType="boolean">
        SELECT COUNT(*) > 0
        FROM manage_menu_role_permissions
        WHERE menu_id = #{menuId}
          AND role_id = #{roleId}
          AND delete_yn = 'N'
        <if test="excludePermissionId != null">
          AND permission_id != #{excludePermissionId}
        </if>
    </select>

    <!-- 특정 메뉴의 모든 역할 권한 삭제 -->
    <update id="deleteAllByMenuId">
        UPDATE manage_menu_role_permissions
        SET delete_yn = 'Y',
            delete_user_email = #{deleteUserEmail},
            delete_date = NOW()
        WHERE menu_id = #{menuId}
          AND delete_yn = 'N'
    </update>

    <!-- 특정 역할의 모든 메뉴 권한 삭제 -->
    <update id="deleteAllByRoleId">
        UPDATE manage_menu_role_permissions
        SET delete_yn = 'Y',
            delete_user_email = #{deleteUserEmail},
            delete_date = NOW()
        WHERE role_id = #{roleId}
          AND delete_yn = 'N'
    </update>

    <!-- 메뉴 역할 권한 일괄 생성 -->
    <insert id="insertMenuRolePermissionBatch" parameterType="java.util.List">
        INSERT INTO manage_menu_role_permissions (
            menu_id, role_id, is_accessible,
            create_user_email, create_date, update_user_email, last_update_date,
            use_yn, delete_yn
        ) VALUES
        <foreach collection="list" item="item" separator=",">
        (
            #{item.menuId}, #{item.roleId}, #{item.isAccessible},
            #{item.createUserEmail}, #{item.createDate}, #{item.updateUserEmail}, #{item.lastUpdateDate},
            #{item.useYn}, #{item.deleteYn}
        )
        </foreach>
    </insert>

</mapper>
