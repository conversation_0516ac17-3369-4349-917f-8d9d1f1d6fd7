<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.wayplus.wayplus_qr.mapper.MenuUserPermissionMapper">

    <!-- MenuUserPermission 엔티티 ResultMap -->
    <resultMap id="menuUserPermissionResultMap" type="kr.wayplus.wayplus_qr.entity.MenuUserPermission">
        <id property="userPermissionId" column="user_permission_id"/>
        <result property="menuId" column="menu_id"/>
        <result property="userEmail" column="user_email"/>
        <result property="isAccessible" column="is_accessible"/>
        <result property="permissionNote" column="permission_note"/>
        <result property="createUserEmail" column="create_user_email"/>
        <result property="createDate" column="create_date"/>
        <result property="updateUserEmail" column="update_user_email"/>
        <result property="lastUpdateDate" column="last_update_date"/>
        <result property="deleteUserEmail" column="delete_user_email"/>
        <result property="deleteDate" column="delete_date"/>
        <result property="useYn" column="use_yn"/>
        <result property="deleteYn" column="delete_yn"/>
    </resultMap>

    <!-- UserPermissionDto ResultMap -->
    <resultMap id="userPermissionDtoResultMap" type="kr.wayplus.wayplus_qr.dto.MenuDto$PermissionResponse$UserPermission">
        <result property="userEmail" column="user_email"/>
        <result property="userName" column="user_name"/>
        <result property="isAccessible" column="is_accessible"/>
        <result property="permissionNote" column="permission_note"/>
        <result property="createDate" column="create_date"/>
    </resultMap>

    <!-- 메뉴 사용자 권한 생성 -->
    <insert id="insertMenuUserPermission" parameterType="kr.wayplus.wayplus_qr.entity.MenuUserPermission" useGeneratedKeys="true" keyProperty="userPermissionId">
        INSERT INTO manage_menu_user_permissions (
            menu_id, user_email, is_accessible, permission_note,
            create_user_email, create_date, update_user_email, last_update_date,
            use_yn, delete_yn
        ) VALUES (
            #{menuId}, #{userEmail}, #{isAccessible}, #{permissionNote},
            #{createUserEmail}, #{createDate}, #{updateUserEmail}, #{lastUpdateDate},
            #{useYn}, #{deleteYn}
        )
    </insert>

    <!-- 메뉴 사용자 권한 수정 -->
    <update id="updateMenuUserPermission" parameterType="kr.wayplus.wayplus_qr.entity.MenuUserPermission">
        UPDATE manage_menu_user_permissions
        SET is_accessible = #{isAccessible},
            permission_note = #{permissionNote},
            update_user_email = #{updateUserEmail},
            last_update_date = #{lastUpdateDate}
        WHERE user_permission_id = #{userPermissionId}
          AND delete_yn = 'N'
    </update>

    <!-- 메뉴 사용자 권한 논리 삭제 -->
    <update id="deleteMenuUserPermission">
        UPDATE manage_menu_user_permissions
        SET delete_yn = 'Y',
            delete_user_email = #{deleteUserEmail},
            delete_date = NOW()
        WHERE user_permission_id = #{userPermissionId}
          AND delete_yn = 'N'
    </update>

    <!-- 메뉴와 사용자로 권한 조회 -->
    <select id="selectByMenuIdAndUserEmail" resultMap="menuUserPermissionResultMap">
        SELECT *
        FROM manage_menu_user_permissions
        WHERE menu_id = #{menuId}
          AND user_email = #{userEmail}
          AND delete_yn = 'N'
    </select>

    <!-- 특정 메뉴의 모든 사용자 권한 조회 -->
    <select id="selectUserPermissionsByMenuId" resultMap="userPermissionDtoResultMap">
        SELECT mup.user_email, u.name as user_name,
               mup.is_accessible, mup.permission_note, mup.create_date
        FROM manage_menu_user_permissions mup
        LEFT JOIN users u ON mup.user_email = u.user_email
        WHERE mup.menu_id = #{menuId}
          AND mup.delete_yn = 'N'
          AND mup.use_yn = 'Y'
        ORDER BY mup.user_email
    </select>

    <!-- 특정 사용자의 모든 메뉴 권한 조회 -->
    <select id="selectMenuPermissionsByUserEmail" resultMap="menuUserPermissionResultMap">
        SELECT *
        FROM manage_menu_user_permissions
        WHERE user_email = #{userEmail}
          AND delete_yn = 'N'
          AND use_yn = 'Y'
        ORDER BY menu_id
    </select>

    <!-- 사용자별 메뉴 접근 가능 여부 확인 -->
    <select id="isAccessibleByUser" resultType="boolean">
        SELECT COUNT(*) > 0
        FROM manage_menu_user_permissions
        WHERE menu_id = #{menuId}
          AND user_email = #{userEmail}
          AND is_accessible = 'Y'
          AND delete_yn = 'N'
          AND use_yn = 'Y'
    </select>

    <!-- 메뉴와 사용자 조합 존재 여부 확인 -->
    <select id="existsByMenuIdAndUserEmail" resultType="boolean">
        SELECT COUNT(*) > 0
        FROM manage_menu_user_permissions
        WHERE menu_id = #{menuId}
          AND user_email = #{userEmail}
          AND delete_yn = 'N'
        <if test="excludeUserPermissionId != null">
          AND user_permission_id != #{excludeUserPermissionId}
        </if>
    </select>

    <!-- 특정 메뉴의 모든 사용자 권한 삭제 -->
    <update id="deleteAllByMenuId">
        UPDATE manage_menu_user_permissions
        SET delete_yn = 'Y',
            delete_user_email = #{deleteUserEmail},
            delete_date = NOW()
        WHERE menu_id = #{menuId}
          AND delete_yn = 'N'
    </update>

    <!-- 특정 사용자의 모든 메뉴 권한 삭제 -->
    <update id="deleteAllByUserEmail">
        UPDATE manage_menu_user_permissions
        SET delete_yn = 'Y',
            delete_user_email = #{deleteUserEmail},
            delete_date = NOW()
        WHERE user_email = #{userEmail}
          AND delete_yn = 'N'
    </update>

    <!-- 메뉴 사용자 권한 일괄 생성 -->
    <insert id="insertMenuUserPermissionBatch" parameterType="java.util.List">
        INSERT INTO manage_menu_user_permissions (
            menu_id, user_email, is_accessible, permission_note,
            create_user_email, create_date, update_user_email, last_update_date,
            use_yn, delete_yn
        ) VALUES
        <foreach collection="list" item="item" separator=",">
        (
            #{item.menuId}, #{item.userEmail}, #{item.isAccessible}, #{item.permissionNote},
            #{item.createUserEmail}, #{item.createDate}, #{item.updateUserEmail}, #{item.lastUpdateDate},
            #{item.useYn}, #{item.deleteYn}
        )
        </foreach>
    </insert>

</mapper>
