package kr.wayplus.wayplus_qr.service;

import kr.wayplus.wayplus_qr.dto.MenuDto;
import kr.wayplus.wayplus_qr.dto.response.ApiResponseDto;
import kr.wayplus.wayplus_qr.entity.MenuEntity;
import kr.wayplus.wayplus_qr.exception.QRcodeException;
import kr.wayplus.wayplus_qr.mapper.ManageMenuMapper;
import kr.wayplus.wayplus_qr.mapper.UserMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("ManageMenuService 통합 테스트")
class ManageMenuServiceTest {

    @Mock
    private ManageMenuMapper manageMenuMapper;

    @Mock
    private UserMapper userMapper;

    @InjectMocks
    private ManageMenuService manageMenuService;

    private MenuDto.CreateRequest validMenuCreateRequest;
    private MenuEntity.Menu existingMenu;

    @BeforeEach
    void setUp() {
        validMenuCreateRequest = MenuDto.CreateRequest.builder()
                .menuCode("TEST_MENU")
                .menuName("테스트 메뉴")
                .menuUrl("/admin/test")
                .menuIcon("fas fa-test")
                .menuLevel(1)
                .displayOrder(1)
                .status("ACTIVE")
                .build();

        existingMenu = MenuEntity.Menu.builder()
                .menuId(1L)
                .menuCode("EXISTING_MENU")
                .menuName("기존 메뉴")
                .menuUrl("/admin/existing")
                .menuLevel(1)
                .displayOrder(1)
                .status("ACTIVE")
                .useYn("Y")
                .deleteYn("N")
                .build();
    }

    @Test
    @DisplayName("메뉴 생성 성공")
    void createMenu_Success() {
        // Given
        String createUserEmail = "<EMAIL>";
        when(menuMapper.existsByMenuCode(validMenuCreateRequest.getMenuCode(), null)).thenReturn(false);
        when(menuMapper.selectMaxDisplayOrder(null, 1)).thenReturn(0);
        when(menuMapper.insertMenu(any(Menu.class))).thenAnswer(invocation -> {
            Menu menu = invocation.getArgument(0);
            menu.setMenuId(1L);
            return 1;
        });

        // When
        ApiResponseDto<Long> result = manageMenuService.createMenu(validMenuCreateRequest, createUserEmail);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData()).isEqualTo(1L);

        verify(menuMapper).existsByMenuCode(validMenuCreateRequest.getMenuCode(), null);
        verify(menuMapper).insertMenu(any(Menu.class));
    }

    @Test
    @DisplayName("메뉴 생성 실패 - 중복된 메뉴 코드")
    void createMenu_Fail_DuplicateMenuCode() {
        // Given
        String createUserEmail = "<EMAIL>";
        when(menuMapper.existsByMenuCode(validMenuCreateRequest.getMenuCode(), null)).thenReturn(true);

        // When & Then
        assertThatThrownBy(() -> manageMenuService.createMenu(validMenuCreateRequest, createUserEmail))
                .isInstanceOf(QRcodeException.class);

        verify(menuMapper).existsByMenuCode(validMenuCreateRequest.getMenuCode(), null);
        verify(menuMapper, never()).insertMenu(any(Menu.class));
    }

    @Test
    @DisplayName("메뉴 트리 조회 성공")
    void getMenuTree_Success() {
        // Given
        List<MenuDto.Response> mockMenus = List.of(
                MenuDto.Response.builder()
                        .menuId(1L)
                        .menuCode("PARENT_MENU")
                        .menuName("상위 메뉴")
                        .menuLevel(1)
                        .displayOrder(1)
                        .accessible(true)
                        .build(),
                MenuDto.Response.builder()
                        .menuId(2L)
                        .parentMenuId(1L)
                        .menuCode("CHILD_MENU")
                        .menuName("하위 메뉴")
                        .menuLevel(2)
                        .displayOrder(1)
                        .accessible(true)
                        .build()
        );
        when(menuMapper.selectAllActiveMenus()).thenReturn(mockMenus);

        // When
        var result = manageMenuService.getMenuTree();

        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getData()).hasSize(1); // 최상위 메뉴만 반환
        assertThat(result.getData().get(0).getChildren()).hasSize(1); // 하위 메뉴 1개

        verify(menuMapper).selectAllActiveMenus();
    }

    @Test
    @DisplayName("메뉴 삭제 성공")
    void deleteMenu_Success() {
        // Given
        Long menuId = 1L;
        String deleteUserEmail = "<EMAIL>";
        when(menuMapper.selectMenuById(menuId)).thenReturn(Optional.of(existingMenu));
        when(menuMapper.hasChildMenus(menuId)).thenReturn(false);
        when(menuMapper.deleteMenu(menuId, deleteUserEmail)).thenReturn(1);
        when(menuRolePermissionMapper.deleteAllByMenuId(menuId, deleteUserEmail)).thenReturn(1);
        when(menuUserPermissionMapper.deleteAllByMenuId(menuId, deleteUserEmail)).thenReturn(1);

        // When
        var result = manageMenuService.deleteMenu(menuId, deleteUserEmail);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();

        verify(menuMapper).selectMenuById(menuId);
        verify(menuMapper).hasChildMenus(menuId);
        verify(menuMapper).deleteMenu(menuId, deleteUserEmail);
        verify(menuRolePermissionMapper).deleteAllByMenuId(menuId, deleteUserEmail);
        verify(menuUserPermissionMapper).deleteAllByMenuId(menuId, deleteUserEmail);
    }

    @Test
    @DisplayName("메뉴 삭제 실패 - 하위 메뉴 존재")
    void deleteMenu_Fail_HasChildren() {
        // Given
        Long menuId = 1L;
        String deleteUserEmail = "<EMAIL>";
        when(menuMapper.selectMenuById(menuId)).thenReturn(Optional.of(existingMenu));
        when(menuMapper.hasChildMenus(menuId)).thenReturn(true);

        // When & Then
        assertThatThrownBy(() -> manageMenuService.deleteMenu(menuId, deleteUserEmail))
                .isInstanceOf(QRcodeException.class);

        verify(menuMapper).selectMenuById(menuId);
        verify(menuMapper).hasChildMenus(menuId);
        verify(menuMapper, never()).deleteMenu(anyLong(), anyString());
    }

    @Test
    @DisplayName("역할별 메뉴 권한 설정 성공")
    void setMenuRolePermission_Success() {
        // Given
        Long menuId = 1L;
        String roleId = "PROJECT_ADMIN";
        String isAccessible = "Y";
        String createUserEmail = "<EMAIL>";

        when(menuMapper.selectMenuById(menuId)).thenReturn(Optional.of(existingMenu));
        when(menuRolePermissionMapper.selectByMenuIdAndRoleId(menuId, roleId)).thenReturn(Optional.empty());
        when(menuRolePermissionMapper.insertMenuRolePermission(any())).thenReturn(1);

        // When
        var result = manageMenuService.setMenuRolePermission(menuId, roleId, isAccessible, createUserEmail);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();

        verify(menuMapper).selectMenuById(menuId);
        verify(menuRolePermissionMapper).selectByMenuIdAndRoleId(menuId, roleId);
        verify(menuRolePermissionMapper).insertMenuRolePermission(any());
    }

    @Test
    @DisplayName("메뉴 접근 권한 확인 - 사용자별 권한 우선")
    void hasMenuAccess_UserPermissionFirst() {
        // Given
        Long menuId = 1L;
        String userEmail = "<EMAIL>";
        String roleId = "SUB_ADMIN";

        when(menuUserPermissionMapper.isAccessibleByUser(menuId, userEmail)).thenReturn(true);

        // When
        boolean result = manageMenuService.hasMenuAccess(menuId, userEmail, roleId);

        // Then
        assertThat(result).isTrue();

        verify(menuUserPermissionMapper).isAccessibleByUser(menuId, userEmail);
        verify(menuRolePermissionMapper, never()).isAccessibleByRole(anyLong(), anyString());
    }

    @Test
    @DisplayName("메뉴 접근 권한 확인 - 역할별 권한 확인")
    void hasMenuAccess_RolePermissionFallback() {
        // Given
        Long menuId = 1L;
        String userEmail = "<EMAIL>";
        String roleId = "SUB_ADMIN";

        when(menuUserPermissionMapper.isAccessibleByUser(menuId, userEmail)).thenReturn(false);
        when(menuRolePermissionMapper.isAccessibleByRole(menuId, roleId)).thenReturn(true);

        // When
        boolean result = manageMenuService.hasMenuAccess(menuId, userEmail, roleId);

        // Then
        assertThat(result).isTrue();

        verify(menuUserPermissionMapper).isAccessibleByUser(menuId, userEmail);
        verify(menuRolePermissionMapper).isAccessibleByRole(menuId, roleId);
    }
}
