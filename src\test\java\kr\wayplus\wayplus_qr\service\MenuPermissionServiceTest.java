package kr.wayplus.wayplus_qr.service;

import kr.wayplus.wayplus_qr.dto.request.MenuRolePermissionRequestDto;
import kr.wayplus.wayplus_qr.dto.request.MenuUserPermissionRequestDto;
import kr.wayplus.wayplus_qr.entity.Menu;
import kr.wayplus.wayplus_qr.entity.MenuRolePermission;
import kr.wayplus.wayplus_qr.entity.MenuUserPermission;
import kr.wayplus.wayplus_qr.entity.User;
import kr.wayplus.wayplus_qr.exception.QRcodeException;
import kr.wayplus.wayplus_qr.mapper.MenuMapper;
import kr.wayplus.wayplus_qr.mapper.MenuRolePermissionMapper;
import kr.wayplus.wayplus_qr.mapper.MenuUserPermissionMapper;
import kr.wayplus.wayplus_qr.mapper.UserMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("MenuPermissionService 테스트")
class MenuPermissionServiceTest {

    @Mock
    private MenuMapper menuMapper;

    @Mock
    private MenuRolePermissionMapper menuRolePermissionMapper;

    @Mock
    private MenuUserPermissionMapper menuUserPermissionMapper;

    @Mock
    private UserMapper userMapper;

    @InjectMocks
    private MenuPermissionService menuPermissionService;

    private Menu testMenu;
    private User testUser;
    private MenuRolePermissionRequestDto rolePermissionRequest;
    private MenuUserPermissionRequestDto userPermissionRequest;

    @BeforeEach
    void setUp() {
        testMenu = Menu.builder()
                .menuId(1L)
                .menuCode("TEST_MENU")
                .menuName("테스트 메뉴")
                .status("ACTIVE")
                .useYn("Y")
                .deleteYn("N")
                .build();

        testUser = User.builder()
                .userEmail("<EMAIL>")
                .name("테스트 사용자")
                .roleId("SUB_ADMIN")
                .useYn("Y")
                .deleteYn("N")
                .build();

        rolePermissionRequest = MenuRolePermissionRequestDto.builder()
                .menuId(1L)
                .roleId("PROJECT_ADMIN")
                .isAccessible("Y")
                .build();

        userPermissionRequest = MenuUserPermissionRequestDto.builder()
                .menuId(1L)
                .userEmail("<EMAIL>")
                .isAccessible("Y")
                .permissionNote("특별 권한 부여")
                .build();
    }

    @Test
    @DisplayName("메뉴 역할 권한 설정 성공 - 새 권한 생성")
    void setMenuRolePermission_Success_NewPermission() {
        // Given
        String createUserEmail = "<EMAIL>";
        when(menuMapper.selectMenuById(1L)).thenReturn(Optional.of(testMenu));
        when(menuRolePermissionMapper.selectByMenuIdAndRoleId(1L, "PROJECT_ADMIN")).thenReturn(Optional.empty());
        when(menuRolePermissionMapper.insertMenuRolePermission(any(MenuRolePermission.class))).thenReturn(1);

        // When
        var result = menuPermissionService.setMenuRolePermission(rolePermissionRequest, createUserEmail);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();

        verify(menuMapper).selectMenuById(1L);
        verify(menuRolePermissionMapper).selectByMenuIdAndRoleId(1L, "PROJECT_ADMIN");
        verify(menuRolePermissionMapper).insertMenuRolePermission(any(MenuRolePermission.class));
    }

    @Test
    @DisplayName("메뉴 역할 권한 설정 성공 - 기존 권한 수정")
    void setMenuRolePermission_Success_UpdatePermission() {
        // Given
        String createUserEmail = "<EMAIL>";
        MenuRolePermission existingPermission = MenuRolePermission.builder()
                .permissionId(1L)
                .menuId(1L)
                .roleId("PROJECT_ADMIN")
                .isAccessible("N")
                .build();

        when(menuMapper.selectMenuById(1L)).thenReturn(Optional.of(testMenu));
        when(menuRolePermissionMapper.selectByMenuIdAndRoleId(1L, "PROJECT_ADMIN")).thenReturn(Optional.of(existingPermission));
        when(menuRolePermissionMapper.updateMenuRolePermission(any(MenuRolePermission.class))).thenReturn(1);

        // When
        var result = menuPermissionService.setMenuRolePermission(rolePermissionRequest, createUserEmail);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();

        verify(menuMapper).selectMenuById(1L);
        verify(menuRolePermissionMapper).selectByMenuIdAndRoleId(1L, "PROJECT_ADMIN");
        verify(menuRolePermissionMapper).updateMenuRolePermission(any(MenuRolePermission.class));
        verify(menuRolePermissionMapper, never()).insertMenuRolePermission(any(MenuRolePermission.class));
    }

    @Test
    @DisplayName("메뉴 역할 권한 설정 실패 - 메뉴 없음")
    void setMenuRolePermission_Fail_MenuNotFound() {
        // Given
        String createUserEmail = "<EMAIL>";
        when(menuMapper.selectMenuById(1L)).thenReturn(Optional.empty());

        // When & Then
        assertThatThrownBy(() -> menuPermissionService.setMenuRolePermission(rolePermissionRequest, createUserEmail))
                .isInstanceOf(QRcodeException.class);

        verify(menuMapper).selectMenuById(1L);
        verify(menuRolePermissionMapper, never()).selectByMenuIdAndRoleId(anyLong(), anyString());
    }

    @Test
    @DisplayName("메뉴 사용자 권한 설정 성공 - 새 권한 생성")
    void setMenuUserPermission_Success_NewPermission() {
        // Given
        String createUserEmail = "<EMAIL>";
        when(menuMapper.selectMenuById(1L)).thenReturn(Optional.of(testMenu));
        when(userMapper.selectUserByEmail("<EMAIL>")).thenReturn(Optional.of(testUser));
        when(menuUserPermissionMapper.selectByMenuIdAndUserEmail(1L, "<EMAIL>")).thenReturn(Optional.empty());
        when(menuUserPermissionMapper.insertMenuUserPermission(any(MenuUserPermission.class))).thenReturn(1);

        // When
        var result = menuPermissionService.setMenuUserPermission(userPermissionRequest, createUserEmail);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();

        verify(menuMapper).selectMenuById(1L);
        verify(userMapper).selectUserByEmail("<EMAIL>");
        verify(menuUserPermissionMapper).selectByMenuIdAndUserEmail(1L, "<EMAIL>");
        verify(menuUserPermissionMapper).insertMenuUserPermission(any(MenuUserPermission.class));
    }

    @Test
    @DisplayName("메뉴 사용자 권한 설정 실패 - 사용자 없음")
    void setMenuUserPermission_Fail_UserNotFound() {
        // Given
        String createUserEmail = "<EMAIL>";
        when(menuMapper.selectMenuById(1L)).thenReturn(Optional.of(testMenu));
        when(userMapper.selectUserByEmail("<EMAIL>")).thenReturn(Optional.empty());

        // When & Then
        assertThatThrownBy(() -> menuPermissionService.setMenuUserPermission(userPermissionRequest, createUserEmail))
                .isInstanceOf(QRcodeException.class);

        verify(menuMapper).selectMenuById(1L);
        verify(userMapper).selectUserByEmail("<EMAIL>");
        verify(menuUserPermissionMapper, never()).selectByMenuIdAndUserEmail(anyLong(), anyString());
    }

    @Test
    @DisplayName("메뉴 접근 권한 확인 - 사용자별 권한 우선")
    void hasMenuAccess_UserPermissionFirst() {
        // Given
        Long menuId = 1L;
        String userEmail = "<EMAIL>";
        String roleId = "SUB_ADMIN";

        when(menuUserPermissionMapper.isAccessibleByUser(menuId, userEmail)).thenReturn(true);

        // When
        boolean result = menuPermissionService.hasMenuAccess(menuId, userEmail, roleId);

        // Then
        assertThat(result).isTrue();

        verify(menuUserPermissionMapper).isAccessibleByUser(menuId, userEmail);
        verify(menuRolePermissionMapper, never()).isAccessibleByRole(anyLong(), anyString());
    }

    @Test
    @DisplayName("메뉴 접근 권한 확인 - 역할별 권한 확인")
    void hasMenuAccess_RolePermissionFallback() {
        // Given
        Long menuId = 1L;
        String userEmail = "<EMAIL>";
        String roleId = "SUB_ADMIN";

        when(menuUserPermissionMapper.isAccessibleByUser(menuId, userEmail)).thenReturn(false);
        when(menuRolePermissionMapper.isAccessibleByRole(menuId, roleId)).thenReturn(true);

        // When
        boolean result = menuPermissionService.hasMenuAccess(menuId, userEmail, roleId);

        // Then
        assertThat(result).isTrue();

        verify(menuUserPermissionMapper).isAccessibleByUser(menuId, userEmail);
        verify(menuRolePermissionMapper).isAccessibleByRole(menuId, roleId);
    }

    @Test
    @DisplayName("메뉴 접근 권한 확인 - 접근 불가")
    void hasMenuAccess_NoAccess() {
        // Given
        Long menuId = 1L;
        String userEmail = "<EMAIL>";
        String roleId = "SUB_ADMIN";

        when(menuUserPermissionMapper.isAccessibleByUser(menuId, userEmail)).thenReturn(false);
        when(menuRolePermissionMapper.isAccessibleByRole(menuId, roleId)).thenReturn(false);

        // When
        boolean result = menuPermissionService.hasMenuAccess(menuId, userEmail, roleId);

        // Then
        assertThat(result).isFalse();

        verify(menuUserPermissionMapper).isAccessibleByUser(menuId, userEmail);
        verify(menuRolePermissionMapper).isAccessibleByRole(menuId, roleId);
    }
}
